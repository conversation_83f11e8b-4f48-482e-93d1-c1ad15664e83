import { useState, useEffect, useCallback } from 'react'

export const useMobileLayout = () => {
  const [isMobileLayout, setIsMobileLayout] = useState(() => {
    // Check localStorage for saved preference
    const saved = localStorage.getItem('tasocode-mobile-layout')
    if (saved !== null) {
      return JSON.parse(saved)
    }
    
    // Default to false (desktop layout)
    return false
  })

  const [isTransitioning, setIsTransitioning] = useState(false)

  // Save preference to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('tasocode-mobile-layout', JSON.stringify(isMobileLayout))
  }, [isMobileLayout])

  // Apply mobile layout class to body for global styling
  useEffect(() => {
    const body = document.body
    
    if (isMobileLayout) {
      body.classList.add('mobile-layout')
    } else {
      body.classList.remove('mobile-layout')
    }

    // Cleanup on unmount
    return () => {
      body.classList.remove('mobile-layout')
    }
  }, [isMobileLayout])

  const toggleMobileLayout = useCallback(() => {
    setIsTransitioning(true)
    
    // Add transition class for smooth animation
    document.body.classList.add('layout-transitioning')
    
    // Toggle the layout
    setIsMobileLayout(prev => !prev)
    
    // Remove transition class after animation completes
    setTimeout(() => {
      setIsTransitioning(false)
      document.body.classList.remove('layout-transitioning')
    }, 300) // Match CSS transition duration
  }, [])

  // Detect actual screen size for automatic mobile detection
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth <= 768
  })

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      const isMobile = width <= 768

      setScreenSize({
        width,
        height,
        isMobile
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Auto-suggest mobile layout on small screens
  const shouldSuggestMobileLayout = screenSize.isMobile && !isMobileLayout

  return {
    isMobileLayout,
    isTransitioning,
    toggleMobileLayout,
    screenSize,
    shouldSuggestMobileLayout
  }
}
