import { Code, Save, Folder<PERSON><PERSON>, FileText, Github } from 'lucide-react'
import './Header.css'

const Header = ({ onSave, onLoad, onNew }) => {
  return (
    <header className="header">
      <div className="header-left">
        <div className="logo">
          <Code className="logo-icon" />
          <h1 className="logo-text">TasoCode</h1>
        </div>
        <span className="tagline">Online Code Compiler</span>
      </div>

      <div className="header-center">
        <nav className="nav-menu">
          <button className="nav-item active">
            <FileText size={16} />
            Editor
          </button>
        </nav>
      </div>

      <div className="header-right">
        <div className="action-buttons">
          <button 
            className="action-btn"
            onClick={onNew}
            title="New Project"
          >
            <FileText size={16} />
            New
          </button>
          
          <button 
            className="action-btn"
            onClick={onLoad}
            title="Load Project"
          >
            <FolderOpen size={16} />
            Load
          </button>
          
          <button 
            className="action-btn primary"
            onClick={onSave}
            title="Save Project"
          >
            <Save size={16} />
            Save
          </button>
        </div>

        <div className="header-divider"></div>

        <a 
          href="https://github.com" 
          target="_blank" 
          rel="noopener noreferrer"
          className="github-link"
          title="View on GitHub"
        >
          <Github size={20} />
        </a>
      </div>
    </header>
  )
}

export default Header
