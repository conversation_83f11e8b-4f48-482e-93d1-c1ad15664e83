.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  min-height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  color: var(--primary-color);
  width: 24px;
  height: 24px;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.tagline {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 400;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-menu {
  display: flex;
  gap: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.nav-item:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-primary);
}

.action-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.action-btn.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.action-btn.primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.header-divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-primary);
}

.mobile-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.mobile-toggle-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.mobile-toggle-btn.active {
  background-color: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.mobile-toggle-btn.active:hover {
  background-color: #d97706;
  border-color: #d97706;
}

.toggle-text {
  font-weight: 500;
}

.github-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.github-link:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 0.5rem 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .header-center {
    order: 3;
    flex-basis: 100%;
    justify-content: flex-start;
  }

  .tagline {
    display: none;
  }

  .action-buttons {
    gap: 0.25rem;
  }

  .action-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .action-btn span {
    display: none;
  }

  .mobile-toggle-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .toggle-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 1.125rem;
  }

  .nav-item {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}
