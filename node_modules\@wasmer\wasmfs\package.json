{"name": "@wasmer/wasmfs", "version": "0.12.0", "description": "Isomorphic library to provide a sandboxed node fs implementation for Node and Browsers. 📂", "main": "lib/index.cjs.js", "module": "lib/index.esm.js", "iife": "lib/index.iife.js", "typings": "lib/index.d.ts", "files": ["src", "lib"], "scripts": {"build": "run-s build:clean build:ts build:prod", "build:clean": "rm -rf lib", "build:ts": "tsc -b", "build:prod": "rollup -c --environment PROD", "build:dev": "rollup -c --environment DEV", "dev": "tsc -b --watch", "test": "jest --config jest.config.js", "test:watch": "jest --config jest.config.js --watch", "docs": "typedoc src/ --out docs --target es6 --theme minimal --mode file"}, "repository": {"type": "git", "url": "git+https://github.com/wasmerio/wasmer-js.git"}, "publishConfig": {"access": "public"}, "keywords": ["wasm", "wasi", "fs", "file", "system", "wapm", "wasmer"], "author": "Wasmer Engineering Team <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wasmerio/wasmer-js/issues"}, "homepage": "https://github.com/wasmerio/wasmer-js/tree/master/packages/wasmfs", "dependencies": {"memfs": "3.0.4", "pako": "^1.0.11", "tar-stream": "^2.1.0"}, "devDependencies": {"@types/tar-stream": "^1.6.1", "jest": "^24.9.0", "typescript": "^3.6.4"}, "gitHead": "539782bfd0693218bacccf7b98d457a378fec182"}