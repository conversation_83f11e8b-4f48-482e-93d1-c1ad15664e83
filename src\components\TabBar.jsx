import { FileTex<PERSON>, Palette, Zap } from 'lucide-react'
import './TabBar.css'

const TabBar = ({ activeTab, onTabChange }) => {
  const tabs = [
    {
      id: 'html',
      name: 'HTML',
      icon: FileText,
      color: '#e34c26'
    },
    {
      id: 'css',
      name: 'CSS',
      icon: Palette,
      color: '#1572b6'
    },
    {
      id: 'javascript',
      name: 'JavaScript',
      icon: Zap,
      color: '#f7df1e'
    }
  ]

  return (
    <div className="tab-bar">
      <div className="tabs">
        {tabs.map((tab) => {
          const Icon = tab.icon
          return (
            <button
              key={tab.id}
              className={`tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => onTabChange(tab.id)}
              style={{
                '--tab-color': tab.color
              }}
            >
              <Icon size={16} className="tab-icon" />
              <span className="tab-name">{tab.name}</span>
              {activeTab === tab.id && (
                <div className="tab-indicator" />
              )}
            </button>
          )
        })}
      </div>
      
      <div className="tab-actions">
        <div className="file-info">
          <span className="file-name">
            {activeTab === 'html' && 'index.html'}
            {activeTab === 'css' && 'styles.css'}
            {activeTab === 'javascript' && 'script.js'}
          </span>
          <div className="file-status saved">●</div>
        </div>
      </div>
    </div>
  )
}

export default TabBar
