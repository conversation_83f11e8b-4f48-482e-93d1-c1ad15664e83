/*
 *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
**************************************************************************** https://mths.be/punycode v1.4.1 by @mathias */
'use strict';var WasmFs=function(Ya){function Yc(c,a,b,d){return new (b||(b=Promise))(function(e,f){function g(c){try{l(d.next(c))}catch(m){f(m)}}function h(c){try{l(d["throw"](c))}catch(m){f(m)}}function l(c){c.done?e(c.value):(new b(function(a){a(c.value)})).then(g,h)}l((d=d.apply(c,a||[])).next())})}function Zc(c,a){function b(c){return function(a){return d([c,a])}}function d(b){if(f)throw new TypeError("Generator is already executing.");for(;e;)try{if(f=1,g&&(h=b[0]&2?g["return"]:b[0]?g["throw"]||
((h=g["return"])&&h.call(g),0):g.next)&&!(h=h.call(g,b[1])).done)return h;if(g=0,h)b=[b[0]&2,h.value];switch(b[0]){case 0:case 1:h=b;break;case 4:return e.label++,{value:b[1],done:!1};case 5:e.label++;g=b[1];b=[0];continue;case 7:b=e.ops.pop();e.trys.pop();continue;default:if(!(h=e.trys,h=0<h.length&&h[h.length-1])&&(6===b[0]||2===b[0])){e=0;continue}if(3===b[0]&&(!h||b[1]>h[0]&&b[1]<h[3]))e.label=b[1];else if(6===b[0]&&e.label<h[1])e.label=h[1],h=b;else if(h&&e.label<h[2])e.label=h[2],e.ops.push(b);
else{h[2]&&e.ops.pop();e.trys.pop();continue}}b=a.call(c,e)}catch(m){b=[6,m],g=0}finally{f=h=0}if(b[0]&5)throw b[1];return{value:b[0]?b[1]:void 0,done:!0}}var e={label:0,sent:function(){if(h[0]&1)throw h[1];return h[1]},trys:[],ops:[]},f,g,h,l;return l={next:b(0),"throw":b(1),"return":b(2)},"function"===typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l}function Za(c){var a="function"===typeof Symbol&&c[Symbol.iterator],b=0;return a?a.call(c):{next:function(){c&&b>=c.length&&(c=void 0);
return{value:c&&c[b++],done:!c}}}}function $c(c,a){var b="function"===typeof Symbol&&c[Symbol.iterator];if(!b)return c;c=b.call(c);var d,e=[];try{for(;(void 0===a||0<a--)&&!(d=c.next()).done;)e.push(d.value)}catch(g){var f={error:g}}finally{try{d&&!d.done&&(b=c["return"])&&b.call(c)}finally{if(f)throw f.error;}}return e}function ad(){for(var c=[],a=0;a<arguments.length;a++)c=c.concat($c(arguments[a]));return c}function D(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?
c["default"]:c}function E(c,a){return a={exports:{}},c(a,a.exports),a.exports}function Db(){$a=!0;for(var c=0;64>c;++c)J[c]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[c],S["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(c)]=c;S[45]=62;S[95]=63}function bd(c,a,b){for(var d=[],e=a;e<b;e+=3)a=(c[e]<<16)+(c[e+1]<<8)+c[e+2],d.push(J[a>>18&63]+J[a>>12&63]+J[a>>6&63]+J[a&63]);return d.join("")}function Eb(c){$a||Db();for(var a=c.length,b=a%3,d="",e=[],
f=0,g=a-b;f<g;f+=16383)e.push(bd(c,f,f+16383>g?g:f+16383));1===b?(c=c[a-1],d+=J[c>>2],d+=J[c<<4&63],d+="=="):2===b&&(c=(c[a-2]<<8)+c[a-1],d+=J[c>>10],d+=J[c>>4&63],d+=J[c<<2&63],d+="=");e.push(d);return e.join("")}function Ba(c,a,b,d,e){var f=8*e-d-1;var g=(1<<f)-1,h=g>>1,l=-7;e=b?e-1:0;var u=b?-1:1,m=c[a+e];e+=u;b=m&(1<<-l)-1;m>>=-l;for(l+=f;0<l;b=256*b+c[a+e],e+=u,l-=8);f=b&(1<<-l)-1;b>>=-l;for(l+=d;0<l;f=256*f+c[a+e],e+=u,l-=8);if(0===b)b=1-h;else{if(b===g)return f?NaN:Infinity*(m?-1:1);f+=Math.pow(2,
d);b-=h}return(m?-1:1)*f*Math.pow(2,b-d)}function Ca(c,a,b,d,e,f){var g,h=8*f-e-1,l=(1<<h)-1,u=l>>1,m=23===e?Math.pow(2,-24)-Math.pow(2,-77):0;f=d?0:f-1;var p=d?1:-1,Ka=0>a||0===a&&0>1/a?1:0;a=Math.abs(a);isNaN(a)||Infinity===a?(a=isNaN(a)?1:0,d=l):(d=Math.floor(Math.log(a)/Math.LN2),1>a*(g=Math.pow(2,-d))&&(d--,g*=2),a=1<=d+u?a+m/g:a+m*Math.pow(2,1-u),2<=a*g&&(d++,g/=2),d+u>=l?(a=0,d=l):1<=d+u?(a=(a*g-1)*Math.pow(2,e),d+=u):(a=a*Math.pow(2,u-1)*Math.pow(2,e),d=0));for(;8<=e;c[b+f]=a&255,f+=p,a/=
256,e-=8);d=d<<e|a;for(h+=e;0<h;c[b+f]=d&255,f+=p,d/=256,h-=8);c[b+f-p]|=128*Ka}function I(c,a){if((k.TYPED_ARRAY_SUPPORT?2147483647:1073741823)<a)throw new RangeError("Invalid typed array length");k.TYPED_ARRAY_SUPPORT?(c=new Uint8Array(a),c.__proto__=k.prototype):(null===c&&(c=new k(a)),c.length=a);return c}function k(c,a,b){if(!(k.TYPED_ARRAY_SUPPORT||this instanceof k))return new k(c,a,b);if("number"===typeof c){if("string"===typeof a)throw Error("If encoding is specified then the first argument must be a string");
return ab(this,c)}return Fb(this,c,a,b)}function Fb(c,a,b,d){if("number"===typeof a)throw new TypeError('"value" argument must not be a number');if("undefined"!==typeof ArrayBuffer&&a instanceof ArrayBuffer){a.byteLength;if(0>b||a.byteLength<b)throw new RangeError("'offset' is out of bounds");if(a.byteLength<b+(d||0))throw new RangeError("'length' is out of bounds");a=void 0===b&&void 0===d?new Uint8Array(a):void 0===d?new Uint8Array(a,b):new Uint8Array(a,b,d);k.TYPED_ARRAY_SUPPORT?(c=a,c.__proto__=
k.prototype):c=bb(c,a);return c}if("string"===typeof a){d=c;c=b;if("string"!==typeof c||""===c)c="utf8";if(!k.isEncoding(c))throw new TypeError('"encoding" must be a valid string encoding');b=Gb(a,c)|0;d=I(d,b);a=d.write(a,c);a!==b&&(d=d.slice(0,a));return d}return cd(c,a)}function Hb(c){if("number"!==typeof c)throw new TypeError('"size" argument must be a number');if(0>c)throw new RangeError('"size" argument must not be negative');}function ab(c,a){Hb(a);c=I(c,0>a?0:cb(a)|0);if(!k.TYPED_ARRAY_SUPPORT)for(var b=
0;b<a;++b)c[b]=0;return c}function bb(c,a){var b=0>a.length?0:cb(a.length)|0;c=I(c,b);for(var d=0;d<b;d+=1)c[d]=a[d]&255;return c}function cd(c,a){if(T(a)){var b=cb(a.length)|0;c=I(c,b);if(0===c.length)return c;a.copy(c,0,0,b);return c}if(a){if("undefined"!==typeof ArrayBuffer&&a.buffer instanceof ArrayBuffer||"length"in a)return(b="number"!==typeof a.length)||(b=a.length,b=b!==b),b?I(c,0):bb(c,a);if("Buffer"===a.type&&Ib(a.data))return bb(c,a.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.");
}function cb(c){if(c>=(k.TYPED_ARRAY_SUPPORT?2147483647:1073741823))throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+(k.TYPED_ARRAY_SUPPORT?2147483647:1073741823).toString(16)+" bytes");return c|0}function T(c){return!(null==c||!c._isBuffer)}function Gb(c,a){if(T(c))return c.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(c)||c instanceof ArrayBuffer))return c.byteLength;"string"!==typeof c&&(c=""+c);var b=c.length;
if(0===b)return 0;for(var d=!1;;)switch(a){case "ascii":case "latin1":case "binary":return b;case "utf8":case "utf-8":case void 0:return Oa(c).length;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return 2*b;case "hex":return b>>>1;case "base64":return Jb(c).length;default:if(d)return Oa(c).length;a=(""+a).toLowerCase();d=!0}}function dd(c,a,b){var d=!1;if(void 0===a||0>a)a=0;if(a>this.length)return"";if(void 0===b||b>this.length)b=this.length;if(0>=b)return"";b>>>=0;a>>>=0;if(b<=a)return"";
for(c||(c="utf8");;)switch(c){case "hex":c=a;a=b;b=this.length;if(!c||0>c)c=0;if(!a||0>a||a>b)a=b;d="";for(b=c;b<a;++b)c=d,d=this[b],d=16>d?"0"+d.toString(16):d.toString(16),d=c+d;return d;case "utf8":case "utf-8":return Kb(this,a,b);case "ascii":c="";for(b=Math.min(this.length,b);a<b;++a)c+=String.fromCharCode(this[a]&127);return c;case "latin1":case "binary":c="";for(b=Math.min(this.length,b);a<b;++a)c+=String.fromCharCode(this[a]);return c;case "base64":return a=0===a&&b===this.length?Eb(this):
Eb(this.slice(a,b)),a;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":a=this.slice(a,b);b="";for(c=0;c<a.length;c+=2)b+=String.fromCharCode(a[c]+256*a[c+1]);return b;default:if(d)throw new TypeError("Unknown encoding: "+c);c=(c+"").toLowerCase();d=!0}}function Z(c,a,b){var d=c[a];c[a]=c[b];c[b]=d}function Lb(c,a,b,d,e){if(0===c.length)return-1;"string"===typeof b?(d=b,b=0):2147483647<b?b=2147483647:-2147483648>b&&(b=-2147483648);b=+b;isNaN(b)&&(b=e?0:c.length-1);0>b&&(b=c.length+b);if(b>=
c.length){if(e)return-1;b=c.length-1}else if(0>b)if(e)b=0;else return-1;"string"===typeof a&&(a=k.from(a,d));if(T(a))return 0===a.length?-1:Mb(c,a,b,d,e);if("number"===typeof a)return a&=255,k.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?e?Uint8Array.prototype.indexOf.call(c,a,b):Uint8Array.prototype.lastIndexOf.call(c,a,b):Mb(c,[a],b,d,e);throw new TypeError("val must be string, number or Buffer");}function Mb(c,a,b,d,e){function f(c,a){return 1===g?c[a]:c.readUInt16BE(a*
g)}var g=1,h=c.length,l=a.length;if(void 0!==d&&(d=String(d).toLowerCase(),"ucs2"===d||"ucs-2"===d||"utf16le"===d||"utf-16le"===d)){if(2>c.length||2>a.length)return-1;g=2;h/=2;l/=2;b/=2}if(e)for(d=-1;b<h;b++)if(f(c,b)===f(a,-1===d?0:b-d)){if(-1===d&&(d=b),b-d+1===l)return d*g}else-1!==d&&(b-=b-d),d=-1;else for(b+l>h&&(b=h-l);0<=b;b--){h=!0;for(d=0;d<l;d++)if(f(c,b+d)!==f(a,d)){h=!1;break}if(h)return b}return-1}function Kb(c,a,b){b=Math.min(c.length,b);for(var d=[];a<b;){var e=c[a],f=null,g=239<e?
4:223<e?3:191<e?2:1;if(a+g<=b)switch(g){case 1:128>e&&(f=e);break;case 2:var h=c[a+1];128===(h&192)&&(e=(e&31)<<6|h&63,127<e&&(f=e));break;case 3:h=c[a+1];var l=c[a+2];128===(h&192)&&128===(l&192)&&(e=(e&15)<<12|(h&63)<<6|l&63,2047<e&&(55296>e||57343<e)&&(f=e));break;case 4:h=c[a+1];l=c[a+2];var u=c[a+3];128===(h&192)&&128===(l&192)&&128===(u&192)&&(e=(e&15)<<18|(h&63)<<12|(l&63)<<6|u&63,65535<e&&1114112>e&&(f=e))}null===f?(f=65533,g=1):65535<f&&(f-=65536,d.push(f>>>10&1023|55296),f=56320|f&1023);
d.push(f);a+=g}c=d.length;if(c<=Nb)d=String.fromCharCode.apply(String,d);else{b="";for(a=0;a<c;)b+=String.fromCharCode.apply(String,d.slice(a,a+=Nb));d=b}return d}function w(c,a,b){if(0!==c%1||0>c)throw new RangeError("offset is not uint");if(c+a>b)throw new RangeError("Trying to access beyond buffer length");}function F(c,a,b,d,e,f){if(!T(c))throw new TypeError('"buffer" argument must be a Buffer instance');if(a>e||a<f)throw new RangeError('"value" argument is out of bounds');if(b+d>c.length)throw new RangeError("Index out of range");
}function Pa(c,a,b,d){0>a&&(a=65535+a+1);for(var e=0,f=Math.min(c.length-b,2);e<f;++e)c[b+e]=(a&255<<8*(d?e:1-e))>>>8*(d?e:1-e)}function Qa(c,a,b,d){0>a&&(a=4294967295+a+1);for(var e=0,f=Math.min(c.length-b,4);e<f;++e)c[b+e]=a>>>8*(d?e:3-e)&255}function Ra(c,a,b,d,e,f){if(b+d>c.length)throw new RangeError("Index out of range");if(0>b)throw new RangeError("Index out of range");}function Oa(c,a){a=a||Infinity;for(var b,d=c.length,e=null,f=[],g=0;g<d;++g){b=c.charCodeAt(g);if(55295<b&&57344>b){if(!e){if(56319<
b){-1<(a-=3)&&f.push(239,191,189);continue}else if(g+1===d){-1<(a-=3)&&f.push(239,191,189);continue}e=b;continue}if(56320>b){-1<(a-=3)&&f.push(239,191,189);e=b;continue}b=(e-55296<<10|b-56320)+65536}else e&&-1<(a-=3)&&f.push(239,191,189);e=null;if(128>b){if(0>--a)break;f.push(b)}else if(2048>b){if(0>(a-=2))break;f.push(b>>6|192,b&63|128)}else if(65536>b){if(0>(a-=3))break;f.push(b>>12|224,b>>6&63|128,b&63|128)}else if(1114112>b){if(0>(a-=4))break;f.push(b>>18|240,b>>12&63|128,b>>6&63|128,b&63|128)}else throw Error("Invalid code point");
}return f}function Ob(c){for(var a=[],b=0;b<c.length;++b)a.push(c.charCodeAt(b)&255);return a}function Jb(c){c=(c.trim?c.trim():c.replace(/^\s+|\s+$/g,"")).replace(ed,"");if(2>c.length)c="";else for(;0!==c.length%4;)c+="=";$a||Db();var a=c.length;if(0<a%4)throw Error("Invalid string. Length must be a multiple of 4");var b="="===c[a-2]?2:"="===c[a-1]?1:0;var d=new fd(3*a/4-b);var e=0<b?a-4:a;var f=0;for(a=0;a<e;a+=4){var g=S[c.charCodeAt(a)]<<18|S[c.charCodeAt(a+1)]<<12|S[c.charCodeAt(a+2)]<<6|S[c.charCodeAt(a+
3)];d[f++]=g>>16&255;d[f++]=g>>8&255;d[f++]=g&255}2===b?(g=S[c.charCodeAt(a)]<<2|S[c.charCodeAt(a+1)]>>4,d[f++]=g&255):1===b&&(g=S[c.charCodeAt(a)]<<10|S[c.charCodeAt(a+1)]<<4|S[c.charCodeAt(a+2)]>>2,d[f++]=g>>8&255,d[f++]=g&255);return d}function ya(c,a,b,d){for(var e=0;e<d&&!(e+b>=a.length||e>=c.length);++e)a[e+b]=c[e];return e}function U(c){return null!=c&&(!!c._isBuffer||Pb(c)||"function"===typeof c.readFloatLE&&"function"===typeof c.slice&&Pb(c.slice(0,0)))}function Pb(c){return!!c.constructor&&
"function"===typeof c.constructor.isBuffer&&c.constructor.isBuffer(c)}function Qb(){throw Error("setTimeout has not been defined");}function Rb(){throw Error("clearTimeout has not been defined");}function Sb(c){if(aa===setTimeout)return setTimeout(c,0);if((aa===Qb||!aa)&&setTimeout)return aa=setTimeout,setTimeout(c,0);try{return aa(c,0)}catch(a){try{return aa.call(null,c,0)}catch(b){return aa.call(this,c,0)}}}function gd(c){if(fa===clearTimeout)return clearTimeout(c);if((fa===Rb||!fa)&&clearTimeout)return fa=
clearTimeout,clearTimeout(c);try{return fa(c)}catch(a){try{return fa.call(null,c)}catch(b){return fa.call(this,c)}}}function hd(){oa&&N&&(oa=!1,N.length?O=N.concat(O):Sa=-1,O.length&&Tb())}function Tb(){if(!oa){var c=Sb(hd);oa=!0;for(var a=O.length;a;){N=O;for(O=[];++Sa<a;)N&&N[Sa].run();Sa=-1;a=O.length}N=null;oa=!1;gd(c)}}function A(c){var a=Array(arguments.length-1);if(1<arguments.length)for(var b=1;b<arguments.length;b++)a[b-1]=arguments[b];O.push(new Ub(c,a));1!==O.length||oa||Sb(Tb)}function Ub(c,
a){this.fun=c;this.array=a}function X(){}function db(c){if(!ha(c)){for(var a=[],b=0;b<arguments.length;b++)a.push(G(arguments[b]));return a.join(" ")}b=1;var d=arguments,e=d.length;a=String(c).replace(id,function(c){if("%%"===c)return"%";if(b>=e)return c;switch(c){case "%s":return String(d[b++]);case "%d":return Number(d[b++]);case "%j":try{return JSON.stringify(d[b++])}catch(h){return"[Circular]"}default:return c}});for(var f=d[b];b<e;f=d[++b])a=null!==f&&ba(f)?a+(" "+G(f)):a+(" "+f);return a}function eb(c,
a){if(P(ia.process))return function(){return eb(c,a).apply(this,arguments)};if(!0===pa.noDeprecation)return c;var b=!1;return function(){if(!b){if(pa.throwDeprecation)throw Error(a);pa.traceDeprecation?console.trace(a):console.error(a);b=!0}return c.apply(this,arguments)}}function Vb(c){P(fb)&&(fb=pa.env.NODE_DEBUG||"");c=c.toUpperCase();Ta[c]||((new RegExp("\\b"+c+"\\b","i")).test(fb)?Ta[c]=function(){var a=db.apply(null,arguments);console.error("%s %d: %s",c,0,a)}:Ta[c]=function(){});return Ta[c]}
function G(c,a){var b={seen:[],stylize:jd};3<=arguments.length&&(b.depth=arguments[2]);4<=arguments.length&&(b.colors=arguments[3]);gb(a)?b.showHidden=a:a&&Wb(b,a);P(b.showHidden)&&(b.showHidden=!1);P(b.depth)&&(b.depth=2);P(b.colors)&&(b.colors=!1);P(b.customInspect)&&(b.customInspect=!0);b.colors&&(b.stylize=kd);return Ua(b,c,b.depth)}function kd(c,a){return(a=G.styles[a])?"\u001b["+G.colors[a][0]+"m"+c+"\u001b["+G.colors[a][1]+"m":c}function jd(c,a){return c}function ld(c){var a={};c.forEach(function(c,
d){a[c]=!0});return a}function Ua(c,a,b){if(c.customInspect&&a&&za(a.inspect)&&a.inspect!==G&&(!a.constructor||a.constructor.prototype!==a)){var d=a.inspect(b,c);ha(d)||(d=Ua(c,d,b));return d}if(d=md(c,a))return d;var e=Object.keys(a),f=ld(e);c.showHidden&&(e=Object.getOwnPropertyNames(a));if(Da(a)&&(0<=e.indexOf("message")||0<=e.indexOf("description")))return hb(a);if(0===e.length){if(za(a))return c.stylize("[Function"+(a.name?": "+a.name:"")+"]","special");if(qa(a))return c.stylize(RegExp.prototype.toString.call(a),
"regexp");if(Ea(a))return c.stylize(Date.prototype.toString.call(a),"date");if(Da(a))return hb(a)}d="";var g=!1,h=["{","}"];Xb(a)&&(g=!0,h=["[","]"]);za(a)&&(d=" [Function"+(a.name?": "+a.name:"")+"]");qa(a)&&(d=" "+RegExp.prototype.toString.call(a));Ea(a)&&(d=" "+Date.prototype.toUTCString.call(a));Da(a)&&(d=" "+hb(a));if(0===e.length&&(!g||0==a.length))return h[0]+d+h[1];if(0>b)return qa(a)?c.stylize(RegExp.prototype.toString.call(a),"regexp"):c.stylize("[Object]","special");c.seen.push(a);e=g?
nd(c,a,b,f,e):e.map(function(d){return ib(c,a,b,f,d,g)});c.seen.pop();return od(e,d,h)}function md(c,a){if(P(a))return c.stylize("undefined","undefined");if(ha(a))return a="'"+JSON.stringify(a).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'",c.stylize(a,"string");if(Yb(a))return c.stylize(""+a,"number");if(gb(a))return c.stylize(""+a,"boolean");if(null===a)return c.stylize("null","null")}function hb(c){return"["+Error.prototype.toString.call(c)+"]"}function nd(c,a,b,d,e){for(var f=
[],g=0,h=a.length;g<h;++g)Object.prototype.hasOwnProperty.call(a,String(g))?f.push(ib(c,a,b,d,String(g),!0)):f.push("");e.forEach(function(e){e.match(/^\d+$/)||f.push(ib(c,a,b,d,e,!0))});return f}function ib(c,a,b,d,e,f){var g,h;a=Object.getOwnPropertyDescriptor(a,e)||{value:a[e]};a.get?h=a.set?c.stylize("[Getter/Setter]","special"):c.stylize("[Getter]","special"):a.set&&(h=c.stylize("[Setter]","special"));Object.prototype.hasOwnProperty.call(d,e)||(g="["+e+"]");h||(0>c.seen.indexOf(a.value)?(h=null===
b?Ua(c,a.value,null):Ua(c,a.value,b-1),-1<h.indexOf("\n")&&(h=f?h.split("\n").map(function(c){return"  "+c}).join("\n").substr(2):"\n"+h.split("\n").map(function(c){return"   "+c}).join("\n"))):h=c.stylize("[Circular]","special"));if(P(g)){if(f&&e.match(/^\d+$/))return h;g=JSON.stringify(""+e);g.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(g=g.substr(1,g.length-2),g=c.stylize(g,"name")):(g=g.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),g=c.stylize(g,"string"))}return g+": "+h}function od(c,
a,b){return 60<c.reduce(function(c,a){a.indexOf("\n");return c+a.replace(/\u001b\[\d\d?m/g,"").length+1},0)?b[0]+(""===a?"":a+"\n ")+" "+c.join(",\n  ")+" "+b[1]:b[0]+a+" "+c.join(", ")+" "+b[1]}function Xb(c){return Array.isArray(c)}function gb(c){return"boolean"===typeof c}function Yb(c){return"number"===typeof c}function ha(c){return"string"===typeof c}function P(c){return void 0===c}function qa(c){return ba(c)&&"[object RegExp]"===Object.prototype.toString.call(c)}function ba(c){return"object"===
typeof c&&null!==c}function Ea(c){return ba(c)&&"[object Date]"===Object.prototype.toString.call(c)}function Da(c){return ba(c)&&("[object Error]"===Object.prototype.toString.call(c)||c instanceof Error)}function za(c){return"function"===typeof c}function jb(c){return null===c||"boolean"===typeof c||"number"===typeof c||"string"===typeof c||"symbol"===typeof c||"undefined"===typeof c}function kb(c){return 10>c?"0"+c.toString(10):c.toString(10)}function pd(){var c=new Date,a=[kb(c.getHours()),kb(c.getMinutes()),
kb(c.getSeconds())].join(":");return[c.getDate(),qd[c.getMonth()],a].join(" ")}function Wb(c,a){if(!a||!ba(a))return c;for(var b=Object.keys(a),d=b.length;d--;)c[b[d]]=a[b[d]];return c}function Zb(c,a){if(c===a)return 0;for(var b=c.length,d=a.length,e=0,f=Math.min(b,d);e<f;++e)if(c[e]!==a[e]){b=c[e];d=a[e];break}return b<d?-1:d<b?1:0}function $b(){return"undefined"!==typeof lb?lb:lb=function(){return"foo"===function(){}.name}()}function ac(c){return U(c)||"function"!==typeof ia.ArrayBuffer?!1:"function"===
typeof ArrayBuffer.isView?ArrayBuffer.isView(c):c?c instanceof DataView||c.buffer&&c.buffer instanceof ArrayBuffer?!0:!1:!1}function x(c,a){c||M(c,!0,a,"==",mb)}function bc(c){if(za(c))return $b()?c.name:(c=c.toString().match(rd))&&c[1]}function nb(c){this.name="AssertionError";this.actual=c.actual;this.expected=c.expected;this.operator=c.operator;c.message?(this.message=c.message,this.generatedMessage=!1):(this.message=cc(dc(this.actual),128)+" "+this.operator+" "+cc(dc(this.expected),128),this.generatedMessage=
!0);var a=c.stackStartFunction||M;Error.captureStackTrace?Error.captureStackTrace(this,a):(c=Error(),c.stack&&(c=c.stack,a=bc(a),a=c.indexOf("\n"+a),0<=a&&(a=c.indexOf("\n",a+1),c=c.substring(a+1)),this.stack=c))}function cc(c,a){return"string"===typeof c?c.length<a?c:c.slice(0,a):c}function dc(c){if($b()||!za(c))return G(c);c=bc(c);return"[Function"+(c?": "+c:"")+"]"}function M(c,a,b,d,e){throw new nb({message:b,actual:c,expected:a,operator:d,stackStartFunction:e});}function mb(c,a){c||M(c,!0,a,
"==",mb)}function ec(c,a,b){c!=a&&M(c,a,b,"==",ec)}function fc(c,a,b){c==a&&M(c,a,b,"!=",fc)}function gc(c,a,b){Aa(c,a,!1)||M(c,a,b,"deepEqual",gc)}function hc(c,a,b){Aa(c,a,!0)||M(c,a,b,"deepStrictEqual",hc)}function Aa(c,a,b,d){if(c===a)return!0;if(U(c)&&U(a))return 0===Zb(c,a);if(Ea(c)&&Ea(a))return c.getTime()===a.getTime();if(qa(c)&&qa(a))return c.source===a.source&&c.global===a.global&&c.multiline===a.multiline&&c.lastIndex===a.lastIndex&&c.ignoreCase===a.ignoreCase;if(null!==c&&"object"===
typeof c||null!==a&&"object"===typeof a){if(!ac(c)||!ac(a)||Object.prototype.toString.call(c)!==Object.prototype.toString.call(a)||c instanceof Float32Array||c instanceof Float64Array){if(U(c)!==U(a))return!1;d=d||{actual:[],expected:[]};var e=d.actual.indexOf(c);if(-1!==e&&e===d.expected.indexOf(a))return!0;d.actual.push(c);d.expected.push(a);return sd(c,a,b,d)}return 0===Zb(new Uint8Array(c.buffer),new Uint8Array(a.buffer))}return b?c===a:c==a}function ic(c){return"[object Arguments]"==Object.prototype.toString.call(c)}
function sd(c,a,b,d){if(null===c||void 0===c||null===a||void 0===a)return!1;if(jb(c)||jb(a))return c===a;if(b&&Object.getPrototypeOf(c)!==Object.getPrototypeOf(a))return!1;var e=ic(c),f=ic(a);if(e&&!f||!e&&f)return!1;if(e)return c=jc.call(c),a=jc.call(a),Aa(c,a,b);e=kc(c);var g=kc(a);if(e.length!==g.length)return!1;e.sort();g.sort();for(f=e.length-1;0<=f;f--)if(e[f]!==g[f])return!1;for(f=e.length-1;0<=f;f--)if(g=e[f],!Aa(c[g],a[g],b,d))return!1;return!0}function lc(c,a,b){Aa(c,a,!1)&&M(c,a,b,"notDeepEqual",
lc)}function mc(c,a,b){Aa(c,a,!0)&&M(c,a,b,"notDeepStrictEqual",mc)}function nc(c,a,b){c!==a&&M(c,a,b,"===",nc)}function oc(c,a,b){c===a&&M(c,a,b,"!==",oc)}function pc(c,a){if(!c||!a)return!1;if("[object RegExp]"==Object.prototype.toString.call(a))return a.test(c);try{if(c instanceof a)return!0}catch(b){}return Error.isPrototypeOf(a)?!1:!0===a.call({},c)}function qc(c,a,b,d){if("function"!==typeof a)throw new TypeError('"block" argument must be a function');"string"===typeof b&&(d=b,b=null);try{a()}catch(h){var e=
h}a=e;d=(b&&b.name?" ("+b.name+").":".")+(d?" "+d:".");c&&!a&&M(a,b,"Missing expected exception"+d);e="string"===typeof d;var f=!c&&Da(a),g=!c&&a&&!b;(f&&e&&pc(a,b)||g)&&M(a,b,"Got unwanted exception"+d);if(c&&a&&b&&!pc(a,b)||!c&&a)throw a;}function td(c,a){for(var b=0,d=c.length-1;0<=d;d--){var e=c[d];"."===e?c.splice(d,1):".."===e?(c.splice(d,1),b++):b&&(c.splice(d,1),b--)}if(a)for(;b--;b)c.unshift("..");return c}function ob(){for(var c="",a=!1,b=arguments.length-1;-1<=b&&!a;b--){var d=0<=b?arguments[b]:
"/";if("string"!==typeof d)throw new TypeError("Arguments to path.resolve must be strings");d&&(c=d+"/"+c,a="/"===d.charAt(0))}c=td(ud(c.split("/"),function(c){return!!c}),!a).join("/");return(a?"/":"")+c||"."}function Va(c,a){function b(c){for(var a=0;a<c.length&&""===c[a];a++);for(var b=c.length-1;0<=b&&""===c[b];b--);return a>b?[]:c.slice(a,b-a+1)}c=ob(c).substr(1);a=ob(a).substr(1);c=b(c.split("/"));a=b(a.split("/"));for(var d=Math.min(c.length,a.length),e=d,f=0;f<d;f++)if(c[f]!==a[f]){e=f;break}d=
[];for(f=e;f<c.length;f++)d.push("..");d=d.concat(a.slice(e));return d.join("/")}function ud(c,a){if(c.filter)return c.filter(a);for(var b=[],d=0;d<c.length;d++)a(c[d],d,c)&&b.push(c[d]);return b}function ja(){}function t(){t.init.call(this)}function rc(c,a,b,d){var e;if("function"!==typeof b)throw new TypeError('"listener" argument must be a function');if(e=c._events){e.newListener&&(c.emit("newListener",a,b.listener?b.listener:b),e=c._events);var f=e[a]}else e=c._events=new ja,c._eventsCount=0;
f?("function"===typeof f?f=e[a]=d?[b,f]:[f,b]:d?f.unshift(b):f.push(b),f.warned||(b=void 0===c._maxListeners?t.defaultMaxListeners:c._maxListeners)&&0<b&&f.length>b&&(f.warned=!0,b=Error("Possible EventEmitter memory leak detected. "+f.length+" "+a+" listeners added. Use emitter.setMaxListeners() to increase limit"),b.name="MaxListenersExceededWarning",b.emitter=c,b.type=a,b.count=f.length,"function"===typeof console.warn?console.warn(b):console.log(b))):(e[a]=b,++c._eventsCount);return c}function sc(c,
a,b){function d(){c.removeListener(a,d);e||(e=!0,b.apply(c,arguments))}var e=!1;d.listener=b;return d}function tc(c){var a=this._events;if(a){c=a[c];if("function"===typeof c)return 1;if(c)return c.length}return 0}function ca(c,a){for(var b=Array(a);a--;)b[a]=c[a];return b}function ka(){this.tail=this.head=null;this.length=0}function ra(c){this.encoding=(c||"utf8").toLowerCase().replace(/[-_]/,"");if(c&&!vd(c))throw Error("Unknown encoding: "+c);switch(this.encoding){case "utf8":this.surrogateSize=
3;break;case "ucs2":case "utf16le":this.surrogateSize=2;this.detectIncompleteChar=wd;break;case "base64":this.surrogateSize=3;this.detectIncompleteChar=xd;break;default:this.write=yd;return}this.charBuffer=new k(6);this.charLength=this.charReceived=0}function yd(c){return c.toString(this.encoding)}function wd(c){this.charLength=(this.charReceived=c.length%2)?2:0}function xd(c){this.charLength=(this.charReceived=c.length%3)?3:0}function zd(c,a,b){if("function"===typeof c.prependListener)return c.prependListener(a,
b);if(c._events&&c._events[a])Array.isArray(c._events[a])?c._events[a].unshift(b):c._events[a]=[b,c._events[a]];else c.on(a,b)}function uc(c,a){c=c||{};this.objectMode=!!c.objectMode;a instanceof B&&(this.objectMode=this.objectMode||!!c.readableObjectMode);a=c.highWaterMark;var b=this.objectMode?16:16384;this.highWaterMark=a||0===a?a:b;this.highWaterMark=~~this.highWaterMark;this.buffer=new ka;this.length=0;this.pipes=null;this.pipesCount=0;this.flowing=null;this.reading=this.endEmitted=this.ended=
!1;this.sync=!0;this.resumeScheduled=this.readableListening=this.emittedReadable=this.needReadable=!1;this.defaultEncoding=c.defaultEncoding||"utf8";this.ranOut=!1;this.awaitDrain=0;this.readingMore=!1;this.encoding=this.decoder=null;c.encoding&&(this.decoder=new ra(c.encoding),this.encoding=c.encoding)}function y(c){if(!(this instanceof y))return new y(c);this._readableState=new uc(c,this);this.readable=!0;c&&"function"===typeof c.read&&(this._read=c.read);t.call(this)}function vc(c,a,b,d,e){var f=
b;var g=null;U(f)||"string"===typeof f||null===f||void 0===f||a.objectMode||(g=new TypeError("Invalid non-string/buffer chunk"));if(f=g)c.emit("error",f);else if(null===b)a.reading=!1,a.ended||(a.decoder&&(b=a.decoder.end())&&b.length&&(a.buffer.push(b),a.length+=a.objectMode?1:b.length),a.ended=!0,Wa(c));else if(a.objectMode||b&&0<b.length)if(a.ended&&!e)c.emit("error",Error("stream.push() after EOF"));else if(a.endEmitted&&e)c.emit("error",Error("stream.unshift() after end event"));else{if(a.decoder&&
!e&&!d){b=a.decoder.write(b);var h=!a.objectMode&&0===b.length}e||(a.reading=!1);h||(a.flowing&&0===a.length&&!a.sync?(c.emit("data",b),c.read(0)):(a.length+=a.objectMode?1:b.length,e?a.buffer.unshift(b):a.buffer.push(b),a.needReadable&&Wa(c)));a.readingMore||(a.readingMore=!0,A(Ad,c,a))}else e||(a.reading=!1);return!a.ended&&(a.needReadable||a.length<a.highWaterMark||0===a.length)}function wc(c,a){if(0>=c||0===a.length&&a.ended)return 0;if(a.objectMode)return 1;if(c!==c)return a.flowing&&a.length?
a.buffer.head.data.length:a.length;if(c>a.highWaterMark){var b=c;8388608<=b?b=8388608:(b--,b|=b>>>1,b|=b>>>2,b|=b>>>4,b|=b>>>8,b|=b>>>16,b++);a.highWaterMark=b}return c<=a.length?c:a.ended?a.length:(a.needReadable=!0,0)}function Wa(c){var a=c._readableState;a.needReadable=!1;a.emittedReadable||(v("emitReadable",a.flowing),a.emittedReadable=!0,a.sync?A(xc,c):xc(c))}function xc(c){v("emit readable");c.emit("readable");pb(c)}function Ad(c,a){for(var b=a.length;!a.reading&&!a.flowing&&!a.ended&&a.length<
a.highWaterMark&&(v("maybeReadMore read 0"),c.read(0),b!==a.length);)b=a.length;a.readingMore=!1}function Bd(c){return function(){var a=c._readableState;v("pipeOnDrain",a.awaitDrain);a.awaitDrain&&a.awaitDrain--;0===a.awaitDrain&&c.listeners("data").length&&(a.flowing=!0,pb(c))}}function Cd(c){v("readable nexttick read 0");c.read(0)}function Dd(c,a){a.reading||(v("resume read 0"),c.read(0));a.resumeScheduled=!1;a.awaitDrain=0;c.emit("resume");pb(c);a.flowing&&!a.reading&&c.read(0)}function pb(c){var a=
c._readableState;for(v("flow",a.flowing);a.flowing&&null!==c.read(););}function yc(c,a){if(0===a.length)return null;if(a.objectMode)var b=a.buffer.shift();else if(!c||c>=a.length)b=a.decoder?a.buffer.join(""):1===a.buffer.length?a.buffer.head.data:a.buffer.concat(a.length),a.buffer.clear();else{b=a.buffer;a=a.decoder;if(c<b.head.data.length)a=b.head.data.slice(0,c),b.head.data=b.head.data.slice(c);else{if(c===b.head.data.length)b=b.shift();else if(a){a=b.head;var d=1,e=a.data;for(c-=e.length;a=a.next;){var f=
a.data,g=c>f.length?f.length:c;e=g===f.length?e+f:e+f.slice(0,c);c-=g;if(0===c){g===f.length?(++d,b.head=a.next?a.next:b.tail=null):(b.head=a,a.data=f.slice(g));break}++d}b.length-=d;b=e}else{a=k.allocUnsafe(c);d=b.head;e=1;d.data.copy(a);for(c-=d.data.length;d=d.next;){f=d.data;g=c>f.length?f.length:c;f.copy(a,a.length-c,0,g);c-=g;if(0===c){g===f.length?(++e,b.head=d.next?d.next:b.tail=null):(b.head=d,d.data=f.slice(g));break}++e}b.length-=e;b=a}a=b}b=a}return b}function qb(c){var a=c._readableState;
if(0<a.length)throw Error('"endReadable()" called on non-empty stream');a.endEmitted||(a.ended=!0,A(Ed,a,c))}function Ed(c,a){c.endEmitted||0!==c.length||(c.endEmitted=!0,a.readable=!1,a.emit("end"))}function Fd(c,a){for(var b=0,d=c.length;b<d;b++)a(c[b],b)}function zc(c,a){for(var b=0,d=c.length;b<d;b++)if(c[b]===a)return b;return-1}function Gd(){}function Hd(c,a,b){this.chunk=c;this.encoding=a;this.callback=b;this.next=null}function rb(c,a){Object.defineProperty(this,"buffer",{get:eb(function(){return this.getBuffer()},
"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.")});c=c||{};this.objectMode=!!c.objectMode;a instanceof B&&(this.objectMode=this.objectMode||!!c.writableObjectMode);var b=c.highWaterMark,d=this.objectMode?16:16384;this.highWaterMark=b||0===b?b:d;this.highWaterMark=~~this.highWaterMark;this.finished=this.ended=this.ending=this.needDrain=!1;this.decodeStrings=!1!==c.decodeStrings;this.defaultEncoding=c.defaultEncoding||"utf8";this.length=0;this.writing=!1;this.corked=0;this.sync=
!0;this.bufferProcessing=!1;this.onwrite=function(c){var b=a._writableState,d=b.sync,e=b.writecb;b.writing=!1;b.writecb=null;b.length-=b.writelen;b.writelen=0;c?(--b.pendingcb,d?A(e,c):e(c),a._writableState.errorEmitted=!0,a.emit("error",c)):((c=Ac(b))||b.corked||b.bufferProcessing||!b.bufferedRequest||Bc(a,b),d?A(Cc,a,b,c,e):Cc(a,b,c,e))};this.writecb=null;this.writelen=0;this.lastBufferedRequest=this.bufferedRequest=null;this.pendingcb=0;this.errorEmitted=this.prefinished=!1;this.bufferedRequestCount=
0;this.corkedRequestsFree=new Dc(this)}function z(c){if(!(this instanceof z||this instanceof B))return new z(c);this._writableState=new rb(c,this);this.writable=!0;c&&("function"===typeof c.write&&(this._write=c.write),"function"===typeof c.writev&&(this._writev=c.writev));t.call(this)}function sb(c,a,b,d,e,f,g){a.writelen=d;a.writecb=g;a.writing=!0;a.sync=!0;b?c._writev(e,a.onwrite):c._write(e,f,a.onwrite);a.sync=!1}function Cc(c,a,b,d){!b&&0===a.length&&a.needDrain&&(a.needDrain=!1,c.emit("drain"));
a.pendingcb--;d();Ec(c,a)}function Bc(c,a){a.bufferProcessing=!0;var b=a.bufferedRequest;if(c._writev&&b&&b.next){var d=Array(a.bufferedRequestCount),e=a.corkedRequestsFree;e.entry=b;for(var f=0;b;)d[f]=b,b=b.next,f+=1;sb(c,a,!0,a.length,d,"",e.finish);a.pendingcb++;a.lastBufferedRequest=null;e.next?(a.corkedRequestsFree=e.next,e.next=null):a.corkedRequestsFree=new Dc(a)}else{for(;b&&(d=b.chunk,sb(c,a,!1,a.objectMode?1:d.length,d,b.encoding,b.callback),b=b.next,!a.writing););null===b&&(a.lastBufferedRequest=
null)}a.bufferedRequestCount=0;a.bufferedRequest=b;a.bufferProcessing=!1}function Ac(c){return c.ending&&0===c.length&&null===c.bufferedRequest&&!c.finished&&!c.writing}function Ec(c,a){var b=Ac(a);b&&(0===a.pendingcb?(a.prefinished||(a.prefinished=!0,c.emit("prefinish")),a.finished=!0,c.emit("finish")):a.prefinished||(a.prefinished=!0,c.emit("prefinish")));return b}function Dc(c){var a=this;this.entry=this.next=null;this.finish=function(b){var d=a.entry;for(a.entry=null;d;){var e=d.callback;c.pendingcb--;
e(b);d=d.next}c.corkedRequestsFree?c.corkedRequestsFree.next=a:c.corkedRequestsFree=a}}function B(c){if(!(this instanceof B))return new B(c);y.call(this,c);z.call(this,c);c&&!1===c.readable&&(this.readable=!1);c&&!1===c.writable&&(this.writable=!1);this.allowHalfOpen=!0;c&&!1===c.allowHalfOpen&&(this.allowHalfOpen=!1);this.once("end",Id)}function Id(){this.allowHalfOpen||this._writableState.ended||A(Jd,this)}function Jd(c){c.end()}function Kd(c){this.afterTransform=function(a,b){var d=c._transformState;
d.transforming=!1;var e=d.writecb;e?(d.writechunk=null,d.writecb=null,null!==b&&void 0!==b&&c.push(b),e(a),a=c._readableState,a.reading=!1,(a.needReadable||a.length<a.highWaterMark)&&c._read(a.highWaterMark),a=void 0):a=c.emit("error",Error("no writecb in Transform class"));return a};this.transforming=this.needTransform=!1;this.writeencoding=this.writechunk=this.writecb=null}function Q(c){if(!(this instanceof Q))return new Q(c);B.call(this,c);this._transformState=new Kd(this);var a=this;this._readableState.needReadable=
!0;this._readableState.sync=!1;c&&("function"===typeof c.transform&&(this._transform=c.transform),"function"===typeof c.flush&&(this._flush=c.flush));this.once("prefinish",function(){"function"===typeof this._flush?this._flush(function(c){Fc(a,c)}):Fc(a)})}function Fc(c,a){if(a)return c.emit("error",a);a=c._transformState;if(c._writableState.length)throw Error("Calling transform done when ws.length != 0");if(a.transforming)throw Error("Calling transform done when still transforming");return c.push(null)}
function la(c){if(!(this instanceof la))return new la(c);Q.call(this,c)}function V(){t.call(this)}function Ld(c,a){var b=c.split("@"),d="";1<b.length&&(d=b[0]+"@",c=b[1]);c=c.replace(Md,".");c=c.split(".");b=c.length;for(var e=[];b--;)e[b]=a(c[b]);a=e.join(".");return d+a}function Gc(c,a){return c+22+75*(26>c)-((0!=a)<<5)}function Nd(c){return Ld(c,function(c){if(Od.test(c)){var a;var d=[];var e=[];var f=0;for(a=c.length;f<a;){var g=c.charCodeAt(f++);if(55296<=g&&56319>=g&&f<a){var h=c.charCodeAt(f++);
56320==(h&64512)?e.push(((g&1023)<<10)+(h&1023)+65536):(e.push(g),f--)}else e.push(g)}c=e;h=c.length;e=128;var l=0;var u=72;for(g=0;g<h;++g){var m=c[g];128>m&&d.push(tb(m))}for((f=a=d.length)&&d.push("-");f<h;){var p=2147483647;for(g=0;g<h;++g)m=c[g],m>=e&&m<p&&(p=m);var Ka=f+1;if(p-e>sa((2147483647-l)/Ka))throw new RangeError(Hc.overflow);l+=(p-e)*Ka;e=p;for(g=0;g<h;++g){m=c[g];if(m<e&&2147483647<++l)throw new RangeError(Hc.overflow);if(m==e){var k=l;for(p=36;;p+=36){m=p<=u?1:p>=u+26?26:p-u;if(k<
m)break;var ta=k-m;k=36-m;d.push(tb(Gc(m+ta%k,0)));k=sa(ta/k)}d.push(tb(Gc(k,0)));u=Ka;p=0;l=f==a?sa(l/700):l>>1;for(l+=sa(l/u);455<l;p+=36)l=sa(l/35);u=sa(p+36*l/(l+38));l=0;++f}}++l;++e}d="xn--"+d.join("")}else d=c;return d})}function Fa(c){switch(typeof c){case "string":return c;case "boolean":return c?"true":"false";case "number":return isFinite(c)?c:"";default:return""}}function Pd(c,a,b,d){a=a||"&";b=b||"=";null===c&&(c=void 0);return"object"===typeof c?Ic(Qd(c),function(d){var e=encodeURIComponent(Fa(d))+
b;return Jc(c[d])?Ic(c[d],function(c){return e+encodeURIComponent(Fa(c))}).join(a):e+encodeURIComponent(Fa(c[d]))}).join(a):d?encodeURIComponent(Fa(d))+b+encodeURIComponent(Fa(c)):""}function Ic(c,a){if(c.map)return c.map(a);for(var b=[],d=0;d<c.length;d++)b.push(a(c[d],d));return b}function Kc(c,a,b,d){b=b||"=";var e={};if("string"!==typeof c||0===c.length)return e;var f=/\+/g;c=c.split(a||"&");a=1E3;d&&"number"===typeof d.maxKeys&&(a=d.maxKeys);d=c.length;0<a&&d>a&&(d=a);for(a=0;a<d;++a){var g=
c[a].replace(f,"%20"),h=g.indexOf(b);if(0<=h){var l=g.substr(0,h);g=g.substr(h+1)}else l=g,g="";l=decodeURIComponent(l);g=decodeURIComponent(g);Object.prototype.hasOwnProperty.call(e,l)?Jc(e[l])?e[l].push(g):e[l]=[e[l],g]:e[l]=g}return e}function K(){this.href=this.path=this.pathname=this.query=this.search=this.hash=this.hostname=this.port=this.host=this.auth=this.slashes=this.protocol=null}function Xa(c,a,b){if(c&&ba(c)&&c instanceof K)return c;var d=new K;d.parse(c,a,b);return d}function Lc(c,a,
b,d){if(!ha(a))throw new TypeError("Parameter 'url' must be a string, not "+typeof a);var e=a.indexOf("?");e=-1!==e&&e<a.indexOf("#")?"?":"#";a=a.split(e);a[0]=a[0].replace(/\\/g,"/");a=a.join(e);e=a.trim();if(!d&&1===a.split("#").length&&(a=Rd.exec(e)))return c.path=e,c.href=e,c.pathname=a[1],a[2]?(c.search=a[2],c.query=b?Kc(c.search.substr(1)):c.search.substr(1)):b&&(c.search="",c.query={}),c;if(a=Sd.exec(e)){a=a[0];var f=a.toLowerCase();c.protocol=f;e=e.substr(a.length)}if(d||a||e.match(/^\/\/[^@\/]+@[^@\/]+/)){var g=
"//"===e.substr(0,2);!g||a&&ub[a]||(e=e.substr(2),c.slashes=!0)}if(!ub[a]&&(g||a&&!ua[a])){a=-1;for(d=0;d<Mc.length;d++)g=e.indexOf(Mc[d]),-1!==g&&(-1===a||g<a)&&(a=g);g=-1===a?e.lastIndexOf("@"):e.lastIndexOf("@",a);-1!==g&&(d=e.slice(0,g),e=e.slice(g+1),c.auth=decodeURIComponent(d));a=-1;for(d=0;d<Nc.length;d++)g=e.indexOf(Nc[d]),-1!==g&&(-1===a||g<a)&&(a=g);-1===a&&(a=e.length);c.host=e.slice(0,a);e=e.slice(a);Oc(c);c.hostname=c.hostname||"";g="["===c.hostname[0]&&"]"===c.hostname[c.hostname.length-
1];if(!g){var h=c.hostname.split(/\./);d=0;for(a=h.length;d<a;d++){var l=h[d];if(l&&!l.match(Pc)){for(var u="",m=0,p=l.length;m<p;m++)u=127<l.charCodeAt(m)?u+"x":u+l[m];if(!u.match(Pc)){a=h.slice(0,d);d=h.slice(d+1);if(l=l.match(Td))a.push(l[1]),d.unshift(l[2]);d.length&&(e="/"+d.join(".")+e);c.hostname=a.join(".");break}}}}c.hostname=c.hostname.length>Ud?"":c.hostname.toLowerCase();g||(c.hostname=Nd(c.hostname));d=c.port?":"+c.port:"";c.host=(c.hostname||"")+d;c.href+=c.host;g&&(c.hostname=c.hostname.substr(1,
c.hostname.length-2),"/"!==e[0]&&(e="/"+e))}if(!Vd[f])for(d=0,a=vb.length;d<a;d++)g=vb[d],-1!==e.indexOf(g)&&(l=encodeURIComponent(g),l===g&&(l=escape(g)),e=e.split(g).join(l));d=e.indexOf("#");-1!==d&&(c.hash=e.substr(d),e=e.slice(0,d));d=e.indexOf("?");-1!==d?(c.search=e.substr(d),c.query=e.substr(d+1),b&&(c.query=Kc(c.query)),e=e.slice(0,d)):b&&(c.search="",c.query={});e&&(c.pathname=e);ua[f]&&c.hostname&&!c.pathname&&(c.pathname="/");if(c.pathname||c.search)d=c.pathname||"",c.path=d+(c.search||
"");c.href=wb(c);return c}function wb(c){var a=c.auth||"";a&&(a=encodeURIComponent(a),a=a.replace(/%3A/i,":"),a+="@");var b=c.protocol||"",d=c.pathname||"",e=c.hash||"",f=!1,g="";c.host?f=a+c.host:c.hostname&&(f=a+(-1===c.hostname.indexOf(":")?c.hostname:"["+this.hostname+"]"),c.port&&(f+=":"+c.port));c.query&&ba(c.query)&&Object.keys(c.query).length&&(g=Pd(c.query));a=c.search||g&&"?"+g||"";b&&":"!==b.substr(-1)&&(b+=":");c.slashes||(!b||ua[b])&&!1!==f?(f="//"+(f||""),d&&"/"!==d.charAt(0)&&(d="/"+
d)):f||(f="");e&&"#"!==e.charAt(0)&&(e="#"+e);a&&"?"!==a.charAt(0)&&(a="?"+a);d=d.replace(/[?#]/g,function(c){return encodeURIComponent(c)});a=a.replace("#","%23");return b+f+d+a+e}function Oc(c){var a=c.host,b=Wd.exec(a);b&&(b=b[0],":"!==b&&(c.port=b.substr(1)),a=a.substr(0,a.length-b.length));a&&(c.hostname=a)}var H="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:{},r=E(function(c,a){Object.defineProperty(a,
"__esModule",{value:!0});a.constants={O_RDONLY:0,O_WRONLY:1,O_RDWR:2,S_IFMT:61440,S_IFREG:32768,S_IFDIR:16384,S_IFCHR:8192,S_IFBLK:24576,S_IFIFO:4096,S_IFLNK:40960,S_IFSOCK:49152,O_CREAT:64,O_EXCL:128,O_NOCTTY:256,O_TRUNC:512,O_APPEND:1024,O_DIRECTORY:65536,O_NOATIME:262144,O_NOFOLLOW:131072,O_SYNC:1052672,O_DIRECT:16384,O_NONBLOCK:2048,S_IRWXU:448,S_IRUSR:256,S_IWUSR:128,S_IXUSR:64,S_IRWXG:56,S_IRGRP:32,S_IWGRP:16,S_IXGRP:8,S_IRWXO:7,S_IROTH:4,S_IWOTH:2,S_IXOTH:1,F_OK:0,R_OK:4,W_OK:2,X_OK:1,UV_FS_SYMLINK_DIR:1,
UV_FS_SYMLINK_JUNCTION:2,UV_FS_COPYFILE_EXCL:1,UV_FS_COPYFILE_FICLONE:2,UV_FS_COPYFILE_FICLONE_FORCE:4,COPYFILE_EXCL:1,COPYFILE_FICLONE:2,COPYFILE_FICLONE_FORCE:4}});D(r);var Xd=E(function(c,a){a.default="function"===typeof BigInt?BigInt:function(){throw Error("BigInt is not supported in this environment.");}}),Ia=E(function(c,a){Object.defineProperty(a,"__esModule",{value:!0});var b=r.constants.S_IFMT,d=r.constants.S_IFDIR,e=r.constants.S_IFREG,f=r.constants.S_IFBLK,g=r.constants.S_IFCHR,h=r.constants.S_IFLNK,
l=r.constants.S_IFIFO,u=r.constants.S_IFSOCK;c=function(){function c(){}c.build=function(a,b){void 0===b&&(b=!1);var d=new c,e=a.gid,f=a.atime,g=a.mtime,m=a.ctime;b=b?Xd.default:function(c){return c};d.uid=b(a.uid);d.gid=b(e);d.rdev=b(0);d.blksize=b(4096);d.ino=b(a.ino);d.size=b(a.getSize());d.blocks=b(1);d.atime=f;d.mtime=g;d.ctime=m;d.birthtime=m;d.atimeMs=b(f.getTime());d.mtimeMs=b(g.getTime());e=b(m.getTime());d.ctimeMs=e;d.birthtimeMs=e;d.dev=b(0);d.mode=b(a.mode);d.nlink=b(a.nlink);return d};
c.prototype._checkModeProperty=function(c){return(Number(this.mode)&b)===c};c.prototype.isDirectory=function(){return this._checkModeProperty(d)};c.prototype.isFile=function(){return this._checkModeProperty(e)};c.prototype.isBlockDevice=function(){return this._checkModeProperty(f)};c.prototype.isCharacterDevice=function(){return this._checkModeProperty(g)};c.prototype.isSymbolicLink=function(){return this._checkModeProperty(h)};c.prototype.isFIFO=function(){return this._checkModeProperty(l)};c.prototype.isSocket=
function(){return this._checkModeProperty(u)};return c}();a.Stats=c;a.default=c});D(Ia);var ia="undefined"!==typeof global?global:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{},J=[],S=[],fd="undefined"!==typeof Uint8Array?Uint8Array:Array,$a=!1,Yd={}.toString,Ib=Array.isArray||function(c){return"[object Array]"==Yd.call(c)};k.TYPED_ARRAY_SUPPORT=void 0!==ia.TYPED_ARRAY_SUPPORT?ia.TYPED_ARRAY_SUPPORT:!0;var Zd=k.TYPED_ARRAY_SUPPORT?2147483647:1073741823;k.poolSize=8192;k._augment=
function(c){c.__proto__=k.prototype;return c};k.from=function(c,a,b){return Fb(null,c,a,b)};k.TYPED_ARRAY_SUPPORT&&(k.prototype.__proto__=Uint8Array.prototype,k.__proto__=Uint8Array);k.alloc=function(c,a,b){Hb(c);c=0>=c?I(null,c):void 0!==a?"string"===typeof b?I(null,c).fill(a,b):I(null,c).fill(a):I(null,c);return c};k.allocUnsafe=function(c){return ab(null,c)};k.allocUnsafeSlow=function(c){return ab(null,c)};k.isBuffer=U;k.compare=function(c,a){if(!T(c)||!T(a))throw new TypeError("Arguments must be Buffers");
if(c===a)return 0;for(var b=c.length,d=a.length,e=0,f=Math.min(b,d);e<f;++e)if(c[e]!==a[e]){b=c[e];d=a[e];break}return b<d?-1:d<b?1:0};k.isEncoding=function(c){switch(String(c).toLowerCase()){case "hex":case "utf8":case "utf-8":case "ascii":case "latin1":case "binary":case "base64":case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return!0;default:return!1}};k.concat=function(c,a){if(!Ib(c))throw new TypeError('"list" argument must be an Array of Buffers');if(0===c.length)return k.alloc(0);
var b;if(void 0===a)for(b=a=0;b<c.length;++b)a+=c[b].length;a=k.allocUnsafe(a);var d=0;for(b=0;b<c.length;++b){var e=c[b];if(!T(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(a,d);d+=e.length}return a};k.byteLength=Gb;k.prototype._isBuffer=!0;k.prototype.swap16=function(){var c=this.length;if(0!==c%2)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var a=0;a<c;a+=2)Z(this,a,a+1);return this};k.prototype.swap32=function(){var c=this.length;if(0!==
c%4)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var a=0;a<c;a+=4)Z(this,a,a+3),Z(this,a+1,a+2);return this};k.prototype.swap64=function(){var c=this.length;if(0!==c%8)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var a=0;a<c;a+=8)Z(this,a,a+7),Z(this,a+1,a+6),Z(this,a+2,a+5),Z(this,a+3,a+4);return this};k.prototype.toString=function(){var c=this.length|0;return 0===c?"":0===arguments.length?Kb(this,0,c):dd.apply(this,arguments)};k.prototype.equals=
function(c){if(!T(c))throw new TypeError("Argument must be a Buffer");return this===c?!0:0===k.compare(this,c)};k.prototype.inspect=function(){var c="";0<this.length&&(c=this.toString("hex",0,50).match(/.{2}/g).join(" "),50<this.length&&(c+=" ... "));return"<Buffer "+c+">"};k.prototype.compare=function(c,a,b,d,e){if(!T(c))throw new TypeError("Argument must be a Buffer");void 0===a&&(a=0);void 0===b&&(b=c?c.length:0);void 0===d&&(d=0);void 0===e&&(e=this.length);if(0>a||b>c.length||0>d||e>this.length)throw new RangeError("out of range index");
if(d>=e&&a>=b)return 0;if(d>=e)return-1;if(a>=b)return 1;a>>>=0;b>>>=0;d>>>=0;e>>>=0;if(this===c)return 0;var f=e-d,g=b-a,h=Math.min(f,g);d=this.slice(d,e);c=c.slice(a,b);for(a=0;a<h;++a)if(d[a]!==c[a]){f=d[a];g=c[a];break}return f<g?-1:g<f?1:0};k.prototype.includes=function(c,a,b){return-1!==this.indexOf(c,a,b)};k.prototype.indexOf=function(c,a,b){return Lb(this,c,a,b,!0)};k.prototype.lastIndexOf=function(c,a,b){return Lb(this,c,a,b,!1)};k.prototype.write=function(c,a,b,d){if(void 0===a)d="utf8",
b=this.length,a=0;else if(void 0===b&&"string"===typeof a)d=a,b=this.length,a=0;else if(isFinite(a))a|=0,isFinite(b)?(b|=0,void 0===d&&(d="utf8")):(d=b,b=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var e=this.length-a;if(void 0===b||b>e)b=e;if(0<c.length&&(0>b||0>a)||a>this.length)throw new RangeError("Attempt to write outside buffer bounds");d||(d="utf8");for(e=!1;;)switch(d){case "hex":a:{a=Number(a)||0;d=this.length-a;b?(b=Number(b),b>d&&
(b=d)):b=d;d=c.length;if(0!==d%2)throw new TypeError("Invalid hex string");b>d/2&&(b=d/2);for(d=0;d<b;++d){e=parseInt(c.substr(2*d,2),16);if(isNaN(e)){c=d;break a}this[a+d]=e}c=d}return c;case "utf8":case "utf-8":return ya(Oa(c,this.length-a),this,a,b);case "ascii":return ya(Ob(c),this,a,b);case "latin1":case "binary":return ya(Ob(c),this,a,b);case "base64":return ya(Jb(c),this,a,b);case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":d=c;e=this.length-a;for(var f=[],g=0;g<d.length&&!(0>(e-=2));++g){var h=
d.charCodeAt(g);c=h>>8;h%=256;f.push(h);f.push(c)}return ya(f,this,a,b);default:if(e)throw new TypeError("Unknown encoding: "+d);d=(""+d).toLowerCase();e=!0}};k.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Nb=4096;k.prototype.slice=function(c,a){var b=this.length;c=~~c;a=void 0===a?b:~~a;0>c?(c+=b,0>c&&(c=0)):c>b&&(c=b);0>a?(a+=b,0>a&&(a=0)):a>b&&(a=b);a<c&&(a=c);if(k.TYPED_ARRAY_SUPPORT)a=this.subarray(c,a),a.__proto__=k.prototype;else{b=
a-c;a=new k(b,void 0);for(var d=0;d<b;++d)a[d]=this[d+c]}return a};k.prototype.readUIntLE=function(c,a,b){c|=0;a|=0;b||w(c,a,this.length);b=this[c];for(var d=1,e=0;++e<a&&(d*=256);)b+=this[c+e]*d;return b};k.prototype.readUIntBE=function(c,a,b){c|=0;a|=0;b||w(c,a,this.length);b=this[c+--a];for(var d=1;0<a&&(d*=256);)b+=this[c+--a]*d;return b};k.prototype.readUInt8=function(c,a){a||w(c,1,this.length);return this[c]};k.prototype.readUInt16LE=function(c,a){a||w(c,2,this.length);return this[c]|this[c+
1]<<8};k.prototype.readUInt16BE=function(c,a){a||w(c,2,this.length);return this[c]<<8|this[c+1]};k.prototype.readUInt32LE=function(c,a){a||w(c,4,this.length);return(this[c]|this[c+1]<<8|this[c+2]<<16)+16777216*this[c+3]};k.prototype.readUInt32BE=function(c,a){a||w(c,4,this.length);return 16777216*this[c]+(this[c+1]<<16|this[c+2]<<8|this[c+3])};k.prototype.readIntLE=function(c,a,b){c|=0;a|=0;b||w(c,a,this.length);b=this[c];for(var d=1,e=0;++e<a&&(d*=256);)b+=this[c+e]*d;b>=128*d&&(b-=Math.pow(2,8*
a));return b};k.prototype.readIntBE=function(c,a,b){c|=0;a|=0;b||w(c,a,this.length);b=a;for(var d=1,e=this[c+--b];0<b&&(d*=256);)e+=this[c+--b]*d;e>=128*d&&(e-=Math.pow(2,8*a));return e};k.prototype.readInt8=function(c,a){a||w(c,1,this.length);return this[c]&128?-1*(255-this[c]+1):this[c]};k.prototype.readInt16LE=function(c,a){a||w(c,2,this.length);c=this[c]|this[c+1]<<8;return c&32768?c|4294901760:c};k.prototype.readInt16BE=function(c,a){a||w(c,2,this.length);c=this[c+1]|this[c]<<8;return c&32768?
c|4294901760:c};k.prototype.readInt32LE=function(c,a){a||w(c,4,this.length);return this[c]|this[c+1]<<8|this[c+2]<<16|this[c+3]<<24};k.prototype.readInt32BE=function(c,a){a||w(c,4,this.length);return this[c]<<24|this[c+1]<<16|this[c+2]<<8|this[c+3]};k.prototype.readFloatLE=function(c,a){a||w(c,4,this.length);return Ba(this,c,!0,23,4)};k.prototype.readFloatBE=function(c,a){a||w(c,4,this.length);return Ba(this,c,!1,23,4)};k.prototype.readDoubleLE=function(c,a){a||w(c,8,this.length);return Ba(this,c,
!0,52,8)};k.prototype.readDoubleBE=function(c,a){a||w(c,8,this.length);return Ba(this,c,!1,52,8)};k.prototype.writeUIntLE=function(c,a,b,d){c=+c;a|=0;b|=0;d||F(this,c,a,b,Math.pow(2,8*b)-1,0);d=1;var e=0;for(this[a]=c&255;++e<b&&(d*=256);)this[a+e]=c/d&255;return a+b};k.prototype.writeUIntBE=function(c,a,b,d){c=+c;a|=0;b|=0;d||F(this,c,a,b,Math.pow(2,8*b)-1,0);d=b-1;var e=1;for(this[a+d]=c&255;0<=--d&&(e*=256);)this[a+d]=c/e&255;return a+b};k.prototype.writeUInt8=function(c,a,b){c=+c;a|=0;b||F(this,
c,a,1,255,0);k.TYPED_ARRAY_SUPPORT||(c=Math.floor(c));this[a]=c&255;return a+1};k.prototype.writeUInt16LE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,2,65535,0);k.TYPED_ARRAY_SUPPORT?(this[a]=c&255,this[a+1]=c>>>8):Pa(this,c,a,!0);return a+2};k.prototype.writeUInt16BE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,2,65535,0);k.TYPED_ARRAY_SUPPORT?(this[a]=c>>>8,this[a+1]=c&255):Pa(this,c,a,!1);return a+2};k.prototype.writeUInt32LE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,4,4294967295,0);k.TYPED_ARRAY_SUPPORT?
(this[a+3]=c>>>24,this[a+2]=c>>>16,this[a+1]=c>>>8,this[a]=c&255):Qa(this,c,a,!0);return a+4};k.prototype.writeUInt32BE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,4,4294967295,0);k.TYPED_ARRAY_SUPPORT?(this[a]=c>>>24,this[a+1]=c>>>16,this[a+2]=c>>>8,this[a+3]=c&255):Qa(this,c,a,!1);return a+4};k.prototype.writeIntLE=function(c,a,b,d){c=+c;a|=0;d||(d=Math.pow(2,8*b-1),F(this,c,a,b,d-1,-d));d=0;var e=1,f=0;for(this[a]=c&255;++d<b&&(e*=256);)0>c&&0===f&&0!==this[a+d-1]&&(f=1),this[a+d]=(c/e>>0)-f&255;return a+
b};k.prototype.writeIntBE=function(c,a,b,d){c=+c;a|=0;d||(d=Math.pow(2,8*b-1),F(this,c,a,b,d-1,-d));d=b-1;var e=1,f=0;for(this[a+d]=c&255;0<=--d&&(e*=256);)0>c&&0===f&&0!==this[a+d+1]&&(f=1),this[a+d]=(c/e>>0)-f&255;return a+b};k.prototype.writeInt8=function(c,a,b){c=+c;a|=0;b||F(this,c,a,1,127,-128);k.TYPED_ARRAY_SUPPORT||(c=Math.floor(c));0>c&&(c=255+c+1);this[a]=c&255;return a+1};k.prototype.writeInt16LE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,2,32767,-32768);k.TYPED_ARRAY_SUPPORT?(this[a]=c&255,
this[a+1]=c>>>8):Pa(this,c,a,!0);return a+2};k.prototype.writeInt16BE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,2,32767,-32768);k.TYPED_ARRAY_SUPPORT?(this[a]=c>>>8,this[a+1]=c&255):Pa(this,c,a,!1);return a+2};k.prototype.writeInt32LE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,4,2147483647,-2147483648);k.TYPED_ARRAY_SUPPORT?(this[a]=c&255,this[a+1]=c>>>8,this[a+2]=c>>>16,this[a+3]=c>>>24):Qa(this,c,a,!0);return a+4};k.prototype.writeInt32BE=function(c,a,b){c=+c;a|=0;b||F(this,c,a,4,2147483647,-2147483648);
0>c&&(c=4294967295+c+1);k.TYPED_ARRAY_SUPPORT?(this[a]=c>>>24,this[a+1]=c>>>16,this[a+2]=c>>>8,this[a+3]=c&255):Qa(this,c,a,!1);return a+4};k.prototype.writeFloatLE=function(c,a,b){b||Ra(this,c,a,4);Ca(this,c,a,!0,23,4);return a+4};k.prototype.writeFloatBE=function(c,a,b){b||Ra(this,c,a,4);Ca(this,c,a,!1,23,4);return a+4};k.prototype.writeDoubleLE=function(c,a,b){b||Ra(this,c,a,8);Ca(this,c,a,!0,52,8);return a+8};k.prototype.writeDoubleBE=function(c,a,b){b||Ra(this,c,a,8);Ca(this,c,a,!1,52,8);return a+
8};k.prototype.copy=function(c,a,b,d){b||(b=0);d||0===d||(d=this.length);a>=c.length&&(a=c.length);a||(a=0);0<d&&d<b&&(d=b);if(d===b||0===c.length||0===this.length)return 0;if(0>a)throw new RangeError("targetStart out of bounds");if(0>b||b>=this.length)throw new RangeError("sourceStart out of bounds");if(0>d)throw new RangeError("sourceEnd out of bounds");d>this.length&&(d=this.length);c.length-a<d-b&&(d=c.length-a+b);var e=d-b;if(this===c&&b<a&&a<d)for(d=e-1;0<=d;--d)c[d+a]=this[d+b];else if(1E3>
e||!k.TYPED_ARRAY_SUPPORT)for(d=0;d<e;++d)c[d+a]=this[d+b];else Uint8Array.prototype.set.call(c,this.subarray(b,b+e),a);return e};k.prototype.fill=function(c,a,b,d){if("string"===typeof c){"string"===typeof a?(d=a,a=0,b=this.length):"string"===typeof b&&(d=b,b=this.length);if(1===c.length){var e=c.charCodeAt(0);256>e&&(c=e)}if(void 0!==d&&"string"!==typeof d)throw new TypeError("encoding must be a string");if("string"===typeof d&&!k.isEncoding(d))throw new TypeError("Unknown encoding: "+d);}else"number"===
typeof c&&(c&=255);if(0>a||this.length<a||this.length<b)throw new RangeError("Out of range index");if(b<=a)return this;a>>>=0;b=void 0===b?this.length:b>>>0;c||(c=0);if("number"===typeof c)for(d=a;d<b;++d)this[d]=c;else for(c=T(c)?c:Oa((new k(c,d)).toString()),e=c.length,d=0;d<b-a;++d)this[d+a]=c[d%e];return this};var ed=/[^+\/0-9A-Za-z-_]/g,Ga=Object.freeze({__proto__:null,INSPECT_MAX_BYTES:50,kMaxLength:Zd,Buffer:k,SlowBuffer:function(c){+c!=c&&(c=0);return k.alloc(+c)},isBuffer:U}),C=E(function(c,
a){function b(c){for(var a=[],b=1;b<arguments.length;b++)a[b-1]=arguments[b];return new (Ga.Buffer.bind.apply(Ga.Buffer,d([void 0,c],a)))}var d=H&&H.__spreadArrays||function(){for(var c=0,a=0,b=arguments.length;a<b;a++)c+=arguments[a].length;c=Array(c);var d=0;for(a=0;a<b;a++)for(var l=arguments[a],u=0,m=l.length;u<m;u++,d++)c[d]=l[u];return c};Object.defineProperty(a,"__esModule",{value:!0});a.Buffer=Ga.Buffer;a.bufferAllocUnsafe=Ga.Buffer.allocUnsafe||b;a.bufferFrom=Ga.Buffer.from||b});D(C);var aa=
Qb,fa=Rb;"function"===typeof ia.setTimeout&&(aa=setTimeout);"function"===typeof ia.clearTimeout&&(fa=clearTimeout);var O=[],oa=!1,N,Sa=-1;Ub.prototype.run=function(){this.fun.apply(null,this.array)};var va=ia.performance||{},$d=va.now||va.mozNow||va.msNow||va.oNow||va.webkitNow||function(){return(new Date).getTime()},ae=new Date,pa={nextTick:A,title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:X,addListener:X,once:X,off:X,removeListener:X,removeAllListeners:X,emit:X,binding:function(c){throw Error("process.binding is not supported");
},cwd:function(){return"/"},chdir:function(c){throw Error("process.chdir is not supported");},umask:function(){return 0},hrtime:function(c){var a=.001*$d.call(va),b=Math.floor(a);a=Math.floor(a%1*1E9);c&&(b-=c[0],a-=c[1],0>a&&(b--,a+=1E9));return[b,a]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-ae)/1E3}},ma="function"===typeof Object.create?function(c,a){c.super_=a;c.prototype=Object.create(a.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}})}:
function(c,a){c.super_=a;var b=function(){};b.prototype=a.prototype;c.prototype=new b;c.prototype.constructor=c},id=/%[sdj%]/g,Ta={},fb;G.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]};G.styles={special:"cyan",number:"yellow","boolean":"yellow",undefined:"grey","null":"bold",string:"green",date:"magenta",regexp:"red"};var qd="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),
La={inherits:ma,_extend:Wb,log:function(){console.log("%s - %s",pd(),db.apply(null,arguments))},isBuffer:function(c){return U(c)},isPrimitive:jb,isFunction:za,isError:Da,isDate:Ea,isObject:ba,isRegExp:qa,isUndefined:P,isSymbol:function(c){return"symbol"===typeof c},isString:ha,isNumber:Yb,isNullOrUndefined:function(c){return null==c},isNull:function(c){return null===c},isBoolean:gb,isArray:Xb,inspect:G,deprecate:eb,format:db,debuglog:Vb},be=Object.prototype.hasOwnProperty,kc=Object.keys||function(c){var a=
[],b;for(b in c)be.call(c,b)&&a.push(b);return a},jc=Array.prototype.slice,lb,rd=/\s*function\s+([^\(\s]*)\s*/;x.AssertionError=nb;ma(nb,Error);x.fail=M;x.ok=mb;x.equal=ec;x.notEqual=fc;x.deepEqual=gc;x.deepStrictEqual=hc;x.notDeepEqual=lc;x.notDeepStrictEqual=mc;x.strictEqual=nc;x.notStrictEqual=oc;x.throws=function(c,a,b){qc(!0,c,a,b)};x.doesNotThrow=function(c,a,b){qc(!1,c,a,b)};x.ifError=function(c){if(c)throw c;};var Ma=E(function(c,a){function b(c){return function(c){function a(a){for(var b=
[],e=1;e<arguments.length;e++)b[e-1]=arguments[e];b=c.call(this,d(a,b))||this;b.code=a;b[h]=a;b.name=c.prototype.name+" ["+b[h]+"]";return b}g(a,c);return a}(c)}function d(c,a){x.strictEqual(typeof c,"string");var b=l[c];x(b,"An invalid error message key was used: "+c+".");if("function"===typeof b)c=b;else{c=La.format;if(void 0===a||0===a.length)return b;a.unshift(b)}return String(c.apply(null,a))}function e(c,a){l[c]="function"===typeof a?a:String(a)}function f(c,a){x(c,"expected is required");x("string"===
typeof a,"thing is required");if(Array.isArray(c)){var b=c.length;x(0<b,"At least one expected value needs to be specified");c=c.map(function(c){return String(c)});return 2<b?"one of "+a+" "+c.slice(0,b-1).join(", ")+", or "+c[b-1]:2===b?"one of "+a+" "+c[0]+" or "+c[1]:"of "+a+" "+c[0]}return"of "+a+" "+String(c)}var g=H&&H.__extends||function(){var c=function(a,b){c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,a){c.__proto__=a}||function(c,a){for(var b in a)a.hasOwnProperty(b)&&
(c[b]=a[b])};return c(a,b)};return function(a,b){function d(){this.constructor=a}c(a,b);a.prototype=null===b?Object.create(b):(d.prototype=b.prototype,new d)}}();Object.defineProperty(a,"__esModule",{value:!0});var h="undefined"===typeof Symbol?"_kCode":Symbol("code"),l={};c=function(c){function b(b){if("object"!==typeof b||null===b)throw new a.TypeError("ERR_INVALID_ARG_TYPE","options","object");var d=b.message?c.call(this,b.message)||this:c.call(this,La.inspect(b.actual).slice(0,128)+" "+(b.operator+
" "+La.inspect(b.expected).slice(0,128)))||this;d.generatedMessage=!b.message;d.name="AssertionError [ERR_ASSERTION]";d.code="ERR_ASSERTION";d.actual=b.actual;d.expected=b.expected;d.operator=b.operator;a.Error.captureStackTrace(d,b.stackStartFunction);return d}g(b,c);return b}(H.Error);a.AssertionError=c;a.message=d;a.E=e;a.Error=b(H.Error);a.TypeError=b(H.TypeError);a.RangeError=b(H.RangeError);e("ERR_ARG_NOT_ITERABLE","%s must be iterable");e("ERR_ASSERTION","%s");e("ERR_BUFFER_OUT_OF_BOUNDS",
function(c,a){return a?"Attempt to write outside buffer bounds":'"'+c+'" is outside of buffer bounds'});e("ERR_CHILD_CLOSED_BEFORE_REPLY","Child closed before reply received");e("ERR_CONSOLE_WRITABLE_STREAM","Console expects a writable stream instance for %s");e("ERR_CPU_USAGE","Unable to obtain cpu usage %s");e("ERR_DNS_SET_SERVERS_FAILED",function(c,a){return'c-ares failed to set servers: "'+c+'" ['+a+"]"});e("ERR_FALSY_VALUE_REJECTION","Promise was rejected with falsy value");e("ERR_ENCODING_NOT_SUPPORTED",
function(c){return'The "'+c+'" encoding is not supported'});e("ERR_ENCODING_INVALID_ENCODED_DATA",function(c){return"The encoded data was not valid for encoding "+c});e("ERR_HTTP_HEADERS_SENT","Cannot render headers after they are sent to the client");e("ERR_HTTP_INVALID_STATUS_CODE","Invalid status code: %s");e("ERR_HTTP_TRAILER_INVALID","Trailers are invalid with this transfer encoding");e("ERR_INDEX_OUT_OF_RANGE","Index out of range");e("ERR_INVALID_ARG_TYPE",function(c,a,b){x(c,"name is required");
if(a.includes("not ")){var d="must not be";a=a.split("not ")[1]}else d="must be";if(Array.isArray(c))d="The "+c.map(function(c){return'"'+c+'"'}).join(", ")+" arguments "+d+" "+f(a,"type");else if(c.includes(" argument"))d="The "+c+" "+d+" "+f(a,"type");else{var e=c.includes(".")?"property":"argument";d='The "'+c+'" '+e+" "+d+" "+f(a,"type")}3<=arguments.length&&(d+=". Received type "+(null!==b?typeof b:"null"));return d});e("ERR_INVALID_ARRAY_LENGTH",function(c,a,b){x.strictEqual(typeof b,"number");
return'The array "'+c+'" (length '+b+") must be of length "+a+"."});e("ERR_INVALID_BUFFER_SIZE","Buffer size must be a multiple of %s");e("ERR_INVALID_CALLBACK","Callback must be a function");e("ERR_INVALID_CHAR","Invalid character in %s");e("ERR_INVALID_CURSOR_POS","Cannot set cursor row without setting its column");e("ERR_INVALID_FD",'"fd" must be a positive integer: %s');e("ERR_INVALID_FILE_URL_HOST",'File URL host must be "localhost" or empty on %s');e("ERR_INVALID_FILE_URL_PATH","File URL path %s");
e("ERR_INVALID_HANDLE_TYPE","This handle type cannot be sent");e("ERR_INVALID_IP_ADDRESS","Invalid IP address: %s");e("ERR_INVALID_OPT_VALUE",function(c,a){return'The value "'+String(a)+'" is invalid for option "'+c+'"'});e("ERR_INVALID_OPT_VALUE_ENCODING",function(c){return'The value "'+String(c)+'" is invalid for option "encoding"'});e("ERR_INVALID_REPL_EVAL_CONFIG",'Cannot specify both "breakEvalOnSigint" and "eval" for REPL');e("ERR_INVALID_SYNC_FORK_INPUT","Asynchronous forks do not support Buffer, Uint8Array or string input: %s");
e("ERR_INVALID_THIS",'Value of "this" must be of type %s');e("ERR_INVALID_TUPLE","%s must be an iterable %s tuple");e("ERR_INVALID_URL","Invalid URL: %s");e("ERR_INVALID_URL_SCHEME",function(c){return"The URL must be "+f(c,"scheme")});e("ERR_IPC_CHANNEL_CLOSED","Channel closed");e("ERR_IPC_DISCONNECTED","IPC channel is already disconnected");e("ERR_IPC_ONE_PIPE","Child process can have only one IPC pipe");e("ERR_IPC_SYNC_FORK","IPC cannot be used with synchronous forks");e("ERR_MISSING_ARGS",function(){for(var c=
[],a=0;a<arguments.length;a++)c[a]=arguments[a];x(0<c.length,"At least one arg needs to be specified");a="The ";var b=c.length;c=c.map(function(c){return'"'+c+'"'});switch(b){case 1:a+=c[0]+" argument";break;case 2:a+=c[0]+" and "+c[1]+" arguments";break;default:a+=c.slice(0,b-1).join(", "),a+=", and "+c[b-1]+" arguments"}return a+" must be specified"});e("ERR_MULTIPLE_CALLBACK","Callback called multiple times");e("ERR_NAPI_CONS_FUNCTION","Constructor must be a function");e("ERR_NAPI_CONS_PROTOTYPE_OBJECT",
"Constructor.prototype must be an object");e("ERR_NO_CRYPTO","Node.js is not compiled with OpenSSL crypto support");e("ERR_NO_LONGER_SUPPORTED","%s is no longer supported");e("ERR_PARSE_HISTORY_DATA","Could not parse history data in %s");e("ERR_SOCKET_ALREADY_BOUND","Socket is already bound");e("ERR_SOCKET_BAD_PORT","Port should be > 0 and < 65536");e("ERR_SOCKET_BAD_TYPE","Bad socket type specified. Valid types are: udp4, udp6");e("ERR_SOCKET_CANNOT_SEND","Unable to send data");e("ERR_SOCKET_CLOSED",
"Socket is closed");e("ERR_SOCKET_DGRAM_NOT_RUNNING","Not running");e("ERR_STDERR_CLOSE","process.stderr cannot be closed");e("ERR_STDOUT_CLOSE","process.stdout cannot be closed");e("ERR_STREAM_WRAP","Stream has StringDecoder set or is in objectMode");e("ERR_TLS_CERT_ALTNAME_INVALID","Hostname/IP does not match certificate's altnames: %s");e("ERR_TLS_DH_PARAM_SIZE",function(c){return"DH parameter size "+c+" is less than 2048"});e("ERR_TLS_HANDSHAKE_TIMEOUT","TLS handshake timeout");e("ERR_TLS_RENEGOTIATION_FAILED",
"Failed to renegotiate");e("ERR_TLS_REQUIRED_SERVER_NAME",'"servername" is required parameter for Server.addContext');e("ERR_TLS_SESSION_ATTACK","TSL session renegotiation attack detected");e("ERR_TRANSFORM_ALREADY_TRANSFORMING","Calling transform done when still transforming");e("ERR_TRANSFORM_WITH_LENGTH_0","Calling transform done when writableState.length != 0");e("ERR_UNKNOWN_ENCODING","Unknown encoding: %s");e("ERR_UNKNOWN_SIGNAL","Unknown signal: %s");e("ERR_UNKNOWN_STDIN_TYPE","Unknown stdin file type");
e("ERR_UNKNOWN_STREAM_TYPE","Unknown stream file type");e("ERR_V8BREAKITERATOR","Full ICU data not installed. See https://github.com/nodejs/node/wiki/Intl")});D(Ma);var Y=E(function(c,a){Object.defineProperty(a,"__esModule",{value:!0});a.ENCODING_UTF8="utf8";a.assertEncoding=function(c){if(c&&!C.Buffer.isEncoding(c))throw new Ma.TypeError("ERR_INVALID_OPT_VALUE_ENCODING",c);};a.strToEncoding=function(c,d){return d&&d!==a.ENCODING_UTF8?"buffer"===d?new C.Buffer(c):(new C.Buffer(c)).toString(d):c}});
D(Y);var yb=E(function(c,a){Object.defineProperty(a,"__esModule",{value:!0});var b=r.constants.S_IFMT,d=r.constants.S_IFDIR,e=r.constants.S_IFREG,f=r.constants.S_IFBLK,g=r.constants.S_IFCHR,h=r.constants.S_IFLNK,l=r.constants.S_IFIFO,u=r.constants.S_IFSOCK;c=function(){function c(){this.name="";this.mode=0}c.build=function(a,b){var d=new c,e=a.getNode().mode;d.name=Y.strToEncoding(a.getName(),b);d.mode=e;return d};c.prototype._checkModeProperty=function(c){return(this.mode&b)===c};c.prototype.isDirectory=
function(){return this._checkModeProperty(d)};c.prototype.isFile=function(){return this._checkModeProperty(e)};c.prototype.isBlockDevice=function(){return this._checkModeProperty(f)};c.prototype.isCharacterDevice=function(){return this._checkModeProperty(g)};c.prototype.isSymbolicLink=function(){return this._checkModeProperty(h)};c.prototype.isFIFO=function(){return this._checkModeProperty(l)};c.prototype.isSocket=function(){return this._checkModeProperty(u)};return c}();a.Dirent=c;a.default=c});
D(yb);"ab".substr(-1);var Ja=E(function(c,a){Object.defineProperty(a,"__esModule",{value:!0});c="function"===typeof setImmediate?setImmediate.bind(H):setTimeout.bind(H);a.default=c});D(Ja);var R=E(function(c,a){function b(){var c=pa;c=c||{};c.getuid||(c.getuid=function(){return 0});c.getgid||(c.getgid=function(){return 0});c.cwd||(c.cwd=function(){return"/"});c.nextTick||(c.nextTick=Ja.default);c.emitWarning||(c.emitWarning=function(c,a){console.warn(""+a+(a?": ":"")+c)});c.env||(c.env={});return c}
Object.defineProperty(a,"__esModule",{value:!0});a.createProcess=b;a.default=b()});D(R);ja.prototype=Object.create(null);t.EventEmitter=t;t.usingDomains=!1;t.prototype.domain=void 0;t.prototype._events=void 0;t.prototype._maxListeners=void 0;t.defaultMaxListeners=10;t.init=function(){this.domain=null;this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new ja,this._eventsCount=0);this._maxListeners=this._maxListeners||void 0};t.prototype.setMaxListeners=function(c){if("number"!==
typeof c||0>c||isNaN(c))throw new TypeError('"n" argument must be a positive number');this._maxListeners=c;return this};t.prototype.getMaxListeners=function(){return void 0===this._maxListeners?t.defaultMaxListeners:this._maxListeners};t.prototype.emit=function(c){var a,b;var d="error"===c;if(a=this._events)d=d&&null==a.error;else if(!d)return!1;var e=this.domain;if(d){a=arguments[1];if(e)a||(a=Error('Uncaught, unspecified "error" event')),a.domainEmitter=this,a.domain=e,a.domainThrown=!1,e.emit("error",
a);else{if(a instanceof Error)throw a;e=Error('Uncaught, unspecified "error" event. ('+a+")");e.context=a;throw e;}return!1}e=a[c];if(!e)return!1;a="function"===typeof e;var f=arguments.length;switch(f){case 1:if(a)e.call(this);else for(a=e.length,e=ca(e,a),d=0;d<a;++d)e[d].call(this);break;case 2:d=arguments[1];if(a)e.call(this,d);else for(a=e.length,e=ca(e,a),f=0;f<a;++f)e[f].call(this,d);break;case 3:d=arguments[1];f=arguments[2];if(a)e.call(this,d,f);else for(a=e.length,e=ca(e,a),b=0;b<a;++b)e[b].call(this,
d,f);break;case 4:d=arguments[1];f=arguments[2];b=arguments[3];if(a)e.call(this,d,f,b);else{a=e.length;e=ca(e,a);for(var g=0;g<a;++g)e[g].call(this,d,f,b)}break;default:d=Array(f-1);for(b=1;b<f;b++)d[b-1]=arguments[b];if(a)e.apply(this,d);else for(a=e.length,e=ca(e,a),f=0;f<a;++f)e[f].apply(this,d)}return!0};t.prototype.addListener=function(c,a){return rc(this,c,a,!1)};t.prototype.on=t.prototype.addListener;t.prototype.prependListener=function(c,a){return rc(this,c,a,!0)};t.prototype.once=function(c,
a){if("function"!==typeof a)throw new TypeError('"listener" argument must be a function');this.on(c,sc(this,c,a));return this};t.prototype.prependOnceListener=function(c,a){if("function"!==typeof a)throw new TypeError('"listener" argument must be a function');this.prependListener(c,sc(this,c,a));return this};t.prototype.removeListener=function(c,a){var b;if("function"!==typeof a)throw new TypeError('"listener" argument must be a function');var d=this._events;if(!d)return this;var e=d[c];if(!e)return this;
if(e===a||e.listener&&e.listener===a)0===--this._eventsCount?this._events=new ja:(delete d[c],d.removeListener&&this.emit("removeListener",c,e.listener||a));else if("function"!==typeof e){var f=-1;for(b=e.length;0<b--;)if(e[b]===a||e[b].listener&&e[b].listener===a){var g=e[b].listener;f=b;break}if(0>f)return this;if(1===e.length){e[0]=void 0;if(0===--this._eventsCount)return this._events=new ja,this;delete d[c]}else{b=f+1;for(var h=e.length;b<h;f+=1,b+=1)e[f]=e[b];e.pop()}d.removeListener&&this.emit("removeListener",
c,g||a)}return this};t.prototype.removeAllListeners=function(c){var a=this._events;if(!a)return this;if(!a.removeListener)return 0===arguments.length?(this._events=new ja,this._eventsCount=0):a[c]&&(0===--this._eventsCount?this._events=new ja:delete a[c]),this;if(0===arguments.length){a=Object.keys(a);for(var b=0,d;b<a.length;++b)d=a[b],"removeListener"!==d&&this.removeAllListeners(d);this.removeAllListeners("removeListener");this._events=new ja;this._eventsCount=0;return this}a=a[c];if("function"===
typeof a)this.removeListener(c,a);else if(a){do this.removeListener(c,a[a.length-1]);while(a[0])}return this};t.prototype.listeners=function(c){var a=this._events;if(a)if(c=a[c])if("function"===typeof c)c=[c.listener||c];else{a=Array(c.length);for(var b=0;b<a.length;++b)a[b]=c[b].listener||c[b];c=a}else c=[];else c=[];return c};t.listenerCount=function(c,a){return"function"===typeof c.listenerCount?c.listenerCount(a):tc.call(c,a)};t.prototype.listenerCount=tc;t.prototype.eventNames=function(){return 0<
this._eventsCount?Reflect.ownKeys(this._events):[]};var Na=E(function(c,a){var b=H&&H.__extends||function(){var c=function(a,b){c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,a){c.__proto__=a}||function(c,a){for(var b in a)a.hasOwnProperty(b)&&(c[b]=a[b])};return c(a,b)};return function(a,b){function d(){this.constructor=a}c(a,b);a.prototype=null===b?Object.create(b):(d.prototype=b.prototype,new d)}}();Object.defineProperty(a,"__esModule",{value:!0});var d=r.constants.S_IFMT,
e=r.constants.S_IFDIR,f=r.constants.S_IFREG,g=r.constants.S_IFLNK,h=r.constants.O_APPEND;a.SEP="/";c=function(c){function a(a,b){void 0===b&&(b=438);var d=c.call(this)||this;d.uid=R.default.getuid();d.gid=R.default.getgid();d.atime=new Date;d.mtime=new Date;d.ctime=new Date;d.perm=438;d.mode=f;d.nlink=1;d.perm=b;d.mode|=b;d.ino=a;return d}b(a,c);a.prototype.getString=function(c){void 0===c&&(c="utf8");return this.getBuffer().toString(c)};a.prototype.setString=function(c){this.buf=C.bufferFrom(c,"utf8");
this.touch()};a.prototype.getBuffer=function(){this.buf||this.setBuffer(C.bufferAllocUnsafe(0));return C.bufferFrom(this.buf)};a.prototype.setBuffer=function(c){this.buf=C.bufferFrom(c);this.touch()};a.prototype.getSize=function(){return this.buf?this.buf.length:0};a.prototype.setModeProperty=function(c){this.mode=this.mode&~d|c};a.prototype.setIsFile=function(){this.setModeProperty(f)};a.prototype.setIsDirectory=function(){this.setModeProperty(e)};a.prototype.setIsSymlink=function(){this.setModeProperty(g)};
a.prototype.isFile=function(){return(this.mode&d)===f};a.prototype.isDirectory=function(){return(this.mode&d)===e};a.prototype.isSymlink=function(){return(this.mode&d)===g};a.prototype.makeSymlink=function(c){this.symlink=c;this.setIsSymlink()};a.prototype.write=function(c,a,b,d){void 0===a&&(a=0);void 0===b&&(b=c.length);void 0===d&&(d=0);this.buf||(this.buf=C.bufferAllocUnsafe(0));if(d+b>this.buf.length){var e=C.bufferAllocUnsafe(d+b);this.buf.copy(e,0,0,this.buf.length);this.buf=e}c.copy(this.buf,
d,a,a+b);this.touch();return b};a.prototype.read=function(c,a,b,d){void 0===a&&(a=0);void 0===b&&(b=c.byteLength);void 0===d&&(d=0);this.buf||(this.buf=C.bufferAllocUnsafe(0));b>c.byteLength&&(b=c.byteLength);b+d>this.buf.length&&(b=this.buf.length-d);this.buf.copy(c,a,d,d+b);return b};a.prototype.truncate=function(c){void 0===c&&(c=0);if(c)if(this.buf||(this.buf=C.bufferAllocUnsafe(0)),c<=this.buf.length)this.buf=this.buf.slice(0,c);else{var a=C.bufferAllocUnsafe(0);this.buf.copy(a);a.fill(0,c)}else this.buf=
C.bufferAllocUnsafe(0);this.touch()};a.prototype.chmod=function(c){this.perm=c;this.mode=this.mode&-512|c;this.touch()};a.prototype.chown=function(c,a){this.uid=c;this.gid=a;this.touch()};a.prototype.touch=function(){this.mtime=new Date;this.emit("change",this)};a.prototype.canRead=function(c,a){void 0===c&&(c=R.default.getuid());void 0===a&&(a=R.default.getgid());return this.perm&4||a===this.gid&&this.perm&32||c===this.uid&&this.perm&256?!0:!1};a.prototype.canWrite=function(c,a){void 0===c&&(c=R.default.getuid());
void 0===a&&(a=R.default.getgid());return this.perm&2||a===this.gid&&this.perm&16||c===this.uid&&this.perm&128?!0:!1};a.prototype.del=function(){this.emit("delete",this)};a.prototype.toJSON=function(){return{ino:this.ino,uid:this.uid,gid:this.gid,atime:this.atime.getTime(),mtime:this.mtime.getTime(),ctime:this.ctime.getTime(),perm:this.perm,mode:this.mode,nlink:this.nlink,symlink:this.symlink,data:this.getString()}};return a}(t.EventEmitter);a.Node=c;c=function(c){function d(a,b,d){var e=c.call(this)||
this;e.children={};e.steps=[];e.ino=0;e.length=0;e.vol=a;e.parent=b;e.steps=b?b.steps.concat([d]):[d];return e}b(d,c);d.prototype.setNode=function(c){this.node=c;this.ino=c.ino};d.prototype.getNode=function(){return this.node};d.prototype.createChild=function(c,a){void 0===a&&(a=this.vol.createNode());var b=new d(this.vol,this,c);b.setNode(a);a.isDirectory();this.setChild(c,b);return b};d.prototype.setChild=function(c,a){void 0===a&&(a=new d(this.vol,this,c));this.children[c]=a;a.parent=this;this.length++;
this.emit("child:add",a,this);return a};d.prototype.deleteChild=function(c){delete this.children[c.getName()];this.length--;this.emit("child:delete",c,this)};d.prototype.getChild=function(c){if(Object.hasOwnProperty.call(this.children,c))return this.children[c]};d.prototype.getPath=function(){return this.steps.join(a.SEP)};d.prototype.getName=function(){return this.steps[this.steps.length-1]};d.prototype.walk=function(c,a,b){void 0===a&&(a=c.length);void 0===b&&(b=0);if(b>=c.length||b>=a)return this;
var d=this.getChild(c[b]);return d?d.walk(c,a,b+1):null};d.prototype.toJSON=function(){return{steps:this.steps,ino:this.ino,children:Object.keys(this.children)}};return d}(t.EventEmitter);a.Link=c;c=function(){function c(c,a,b,d){this.position=0;this.link=c;this.node=a;this.flags=b;this.fd=d}c.prototype.getString=function(c){return this.node.getString()};c.prototype.setString=function(c){this.node.setString(c)};c.prototype.getBuffer=function(){return this.node.getBuffer()};c.prototype.setBuffer=function(c){this.node.setBuffer(c)};
c.prototype.getSize=function(){return this.node.getSize()};c.prototype.truncate=function(c){this.node.truncate(c)};c.prototype.seekTo=function(c){this.position=c};c.prototype.stats=function(){return Ia.default.build(this.node)};c.prototype.write=function(c,a,b,d){void 0===a&&(a=0);void 0===b&&(b=c.length);"number"!==typeof d&&(d=this.position);this.flags&h&&(d=this.getSize());c=this.node.write(c,a,b,d);this.position=d+c;return c};c.prototype.read=function(c,a,b,d){void 0===a&&(a=0);void 0===b&&(b=
c.byteLength);"number"!==typeof d&&(d=this.position);c=this.node.read(c,a,b,d);this.position=d+c;return c};c.prototype.chmod=function(c){this.node.chmod(c)};c.prototype.chown=function(c,a){this.node.chown(c,a)};return c}();a.File=c});D(Na);var ce=Na.Node,Qc=E(function(c,a){Object.defineProperty(a,"__esModule",{value:!0});a.default=function(c,a,e){var b=setTimeout.apply(null,arguments);b&&"object"===typeof b&&"function"===typeof b.unref&&b.unref();return b}});D(Qc);ka.prototype.push=function(c){c=
{data:c,next:null};0<this.length?this.tail.next=c:this.head=c;this.tail=c;++this.length};ka.prototype.unshift=function(c){c={data:c,next:this.head};0===this.length&&(this.tail=c);this.head=c;++this.length};ka.prototype.shift=function(){if(0!==this.length){var c=this.head.data;this.head=1===this.length?this.tail=null:this.head.next;--this.length;return c}};ka.prototype.clear=function(){this.head=this.tail=null;this.length=0};ka.prototype.join=function(c){if(0===this.length)return"";for(var a=this.head,
b=""+a.data;a=a.next;)b+=c+a.data;return b};ka.prototype.concat=function(c){if(0===this.length)return k.alloc(0);if(1===this.length)return this.head.data;c=k.allocUnsafe(c>>>0);for(var a=this.head,b=0;a;)a.data.copy(c,b),b+=a.data.length,a=a.next;return c};var vd=k.isEncoding||function(c){switch(c&&c.toLowerCase()){case "hex":case "utf8":case "utf-8":case "ascii":case "binary":case "base64":case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":case "raw":return!0;default:return!1}};ra.prototype.write=
function(c){for(var a="";this.charLength;){a=c.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:c.length;c.copy(this.charBuffer,this.charReceived,0,a);this.charReceived+=a;if(this.charReceived<this.charLength)return"";c=c.slice(a,c.length);a=this.charBuffer.slice(0,this.charLength).toString(this.encoding);var b=a.charCodeAt(a.length-1);if(55296<=b&&56319>=b)this.charLength+=this.surrogateSize,a="";else{this.charReceived=this.charLength=0;if(0===c.length)return a;break}}this.detectIncompleteChar(c);
var d=c.length;this.charLength&&(c.copy(this.charBuffer,0,c.length-this.charReceived,d),d-=this.charReceived);a+=c.toString(this.encoding,0,d);d=a.length-1;b=a.charCodeAt(d);return 55296<=b&&56319>=b?(b=this.surrogateSize,this.charLength+=b,this.charReceived+=b,this.charBuffer.copy(this.charBuffer,b,0,b),c.copy(this.charBuffer,0,0,b),a.substring(0,d)):a};ra.prototype.detectIncompleteChar=function(c){for(var a=3<=c.length?3:c.length;0<a;a--){var b=c[c.length-a];if(1==a&&6==b>>5){this.charLength=2;
break}if(2>=a&&14==b>>4){this.charLength=3;break}if(3>=a&&30==b>>3){this.charLength=4;break}}this.charReceived=a};ra.prototype.end=function(c){var a="";c&&c.length&&(a=this.write(c));this.charReceived&&(c=this.encoding,a+=this.charBuffer.slice(0,this.charReceived).toString(c));return a};y.ReadableState=uc;var v=Vb("stream");ma(y,t);y.prototype.push=function(c,a){var b=this._readableState;b.objectMode||"string"!==typeof c||(a=a||b.defaultEncoding,a!==b.encoding&&(c=k.from(c,a),a=""));return vc(this,
b,c,a,!1)};y.prototype.unshift=function(c){return vc(this,this._readableState,c,"",!0)};y.prototype.isPaused=function(){return!1===this._readableState.flowing};y.prototype.setEncoding=function(c){this._readableState.decoder=new ra(c);this._readableState.encoding=c;return this};y.prototype.read=function(c){v("read",c);c=parseInt(c,10);var a=this._readableState,b=c;0!==c&&(a.emittedReadable=!1);if(0===c&&a.needReadable&&(a.length>=a.highWaterMark||a.ended))return v("read: emitReadable",a.length,a.ended),
0===a.length&&a.ended?qb(this):Wa(this),null;c=wc(c,a);if(0===c&&a.ended)return 0===a.length&&qb(this),null;var d=a.needReadable;v("need readable",d);if(0===a.length||a.length-c<a.highWaterMark)d=!0,v("length less than watermark",d);a.ended||a.reading?v("reading or ended",!1):d&&(v("do read"),a.reading=!0,a.sync=!0,0===a.length&&(a.needReadable=!0),this._read(a.highWaterMark),a.sync=!1,a.reading||(c=wc(b,a)));d=0<c?yc(c,a):null;null===d?(a.needReadable=!0,c=0):a.length-=c;0===a.length&&(a.ended||
(a.needReadable=!0),b!==c&&a.ended&&qb(this));null!==d&&this.emit("data",d);return d};y.prototype._read=function(c){this.emit("error",Error("not implemented"))};y.prototype.pipe=function(c,a){function b(c){v("onunpipe");c===m&&e()}function d(){v("onend");c.end()}function e(){v("cleanup");c.removeListener("close",h);c.removeListener("finish",l);c.removeListener("drain",r);c.removeListener("error",g);c.removeListener("unpipe",b);m.removeListener("end",d);m.removeListener("end",e);m.removeListener("data",
f);t=!0;!p.awaitDrain||c._writableState&&!c._writableState.needDrain||r()}function f(a){v("ondata");ta=!1;!1!==c.write(a)||ta||((1===p.pipesCount&&p.pipes===c||1<p.pipesCount&&-1!==zc(p.pipes,c))&&!t&&(v("false write response, pause",m._readableState.awaitDrain),m._readableState.awaitDrain++,ta=!0),m.pause())}function g(a){v("onerror",a);k();c.removeListener("error",g);0===c.listeners("error").length&&c.emit("error",a)}function h(){c.removeListener("finish",l);k()}function l(){v("onfinish");c.removeListener("close",
h);k()}function k(){v("unpipe");m.unpipe(c)}var m=this,p=this._readableState;switch(p.pipesCount){case 0:p.pipes=c;break;case 1:p.pipes=[p.pipes,c];break;default:p.pipes.push(c)}p.pipesCount+=1;v("pipe count=%d opts=%j",p.pipesCount,a);a=a&&!1===a.end?e:d;if(p.endEmitted)A(a);else m.once("end",a);c.on("unpipe",b);var r=Bd(m);c.on("drain",r);var t=!1,ta=!1;m.on("data",f);zd(c,"error",g);c.once("close",h);c.once("finish",l);c.emit("pipe",m);p.flowing||(v("pipe resume"),m.resume());return c};y.prototype.unpipe=
function(c){var a=this._readableState;if(0===a.pipesCount)return this;if(1===a.pipesCount){if(c&&c!==a.pipes)return this;c||(c=a.pipes);a.pipes=null;a.pipesCount=0;a.flowing=!1;c&&c.emit("unpipe",this);return this}if(!c){c=a.pipes;var b=a.pipesCount;a.pipes=null;a.pipesCount=0;a.flowing=!1;for(a=0;a<b;a++)c[a].emit("unpipe",this);return this}b=zc(a.pipes,c);if(-1===b)return this;a.pipes.splice(b,1);--a.pipesCount;1===a.pipesCount&&(a.pipes=a.pipes[0]);c.emit("unpipe",this);return this};y.prototype.on=
function(c,a){a=t.prototype.on.call(this,c,a);"data"===c?!1!==this._readableState.flowing&&this.resume():"readable"===c&&(c=this._readableState,c.endEmitted||c.readableListening||(c.readableListening=c.needReadable=!0,c.emittedReadable=!1,c.reading?c.length&&Wa(this):A(Cd,this)));return a};y.prototype.addListener=y.prototype.on;y.prototype.resume=function(){var c=this._readableState;c.flowing||(v("resume"),c.flowing=!0,c.resumeScheduled||(c.resumeScheduled=!0,A(Dd,this,c)));return this};y.prototype.pause=
function(){v("call pause flowing=%j",this._readableState.flowing);!1!==this._readableState.flowing&&(v("pause"),this._readableState.flowing=!1,this.emit("pause"));return this};y.prototype.wrap=function(c){var a=this._readableState,b=!1,d=this;c.on("end",function(){v("wrapped end");if(a.decoder&&!a.ended){var c=a.decoder.end();c&&c.length&&d.push(c)}d.push(null)});c.on("data",function(e){v("wrapped data");a.decoder&&(e=a.decoder.write(e));a.objectMode&&(null===e||void 0===e)||!(a.objectMode||e&&e.length)||
d.push(e)||(b=!0,c.pause())});for(var e in c)void 0===this[e]&&"function"===typeof c[e]&&(this[e]=function(a){return function(){return c[a].apply(c,arguments)}}(e));Fd(["error","close","destroy","pause","resume"],function(a){c.on(a,d.emit.bind(d,a))});d._read=function(a){v("wrapped _read",a);b&&(b=!1,c.resume())};return d};y._fromList=yc;z.WritableState=rb;ma(z,t);rb.prototype.getBuffer=function(){for(var c=this.bufferedRequest,a=[];c;)a.push(c),c=c.next;return a};z.prototype.pipe=function(){this.emit("error",
Error("Cannot pipe, not readable"))};z.prototype.write=function(c,a,b){var d=this._writableState,e=!1;"function"===typeof a&&(b=a,a=null);k.isBuffer(c)?a="buffer":a||(a=d.defaultEncoding);"function"!==typeof b&&(b=Gd);if(d.ended)d=b,c=Error("write after end"),this.emit("error",c),A(d,c);else{var f=b,g=!0,h=!1;null===c?h=new TypeError("May not write null values to stream"):k.isBuffer(c)||"string"===typeof c||void 0===c||d.objectMode||(h=new TypeError("Invalid non-string/buffer chunk"));h&&(this.emit("error",
h),A(f,h),g=!1);g&&(d.pendingcb++,e=a,d.objectMode||!1===d.decodeStrings||"string"!==typeof c||(c=k.from(c,e)),k.isBuffer(c)&&(e="buffer"),f=d.objectMode?1:c.length,d.length+=f,a=d.length<d.highWaterMark,a||(d.needDrain=!0),d.writing||d.corked?(f=d.lastBufferedRequest,d.lastBufferedRequest=new Hd(c,e,b),f?f.next=d.lastBufferedRequest:d.bufferedRequest=d.lastBufferedRequest,d.bufferedRequestCount+=1):sb(this,d,!1,f,c,e,b),e=a)}return e};z.prototype.cork=function(){this._writableState.corked++};z.prototype.uncork=
function(){var c=this._writableState;c.corked&&(c.corked--,c.writing||c.corked||c.finished||c.bufferProcessing||!c.bufferedRequest||Bc(this,c))};z.prototype.setDefaultEncoding=function(c){"string"===typeof c&&(c=c.toLowerCase());if(!(-1<"hex utf8 utf-8 ascii binary base64 ucs2 ucs-2 utf16le utf-16le raw".split(" ").indexOf((c+"").toLowerCase())))throw new TypeError("Unknown encoding: "+c);this._writableState.defaultEncoding=c;return this};z.prototype._write=function(c,a,b){b(Error("not implemented"))};
z.prototype._writev=null;z.prototype.end=function(c,a,b){var d=this._writableState;"function"===typeof c?(b=c,a=c=null):"function"===typeof a&&(b=a,a=null);null!==c&&void 0!==c&&this.write(c,a);d.corked&&(d.corked=1,this.uncork());if(!d.ending&&!d.finished){c=b;d.ending=!0;Ec(this,d);if(c)if(d.finished)A(c);else this.once("finish",c);d.ended=!0;this.writable=!1}};ma(B,y);for(var Rc=Object.keys(z.prototype),zb=0;zb<Rc.length;zb++){var Ab=Rc[zb];B.prototype[Ab]||(B.prototype[Ab]=z.prototype[Ab])}ma(Q,
B);Q.prototype.push=function(c,a){this._transformState.needTransform=!1;return B.prototype.push.call(this,c,a)};Q.prototype._transform=function(c,a,b){throw Error("Not implemented");};Q.prototype._write=function(c,a,b){var d=this._transformState;d.writecb=b;d.writechunk=c;d.writeencoding=a;d.transforming||(c=this._readableState,(d.needTransform||c.needReadable||c.length<c.highWaterMark)&&this._read(c.highWaterMark))};Q.prototype._read=function(c){c=this._transformState;null!==c.writechunk&&c.writecb&&
!c.transforming?(c.transforming=!0,this._transform(c.writechunk,c.writeencoding,c.afterTransform)):c.needTransform=!0};ma(la,Q);la.prototype._transform=function(c,a,b){b(null,c)};ma(V,t);V.Readable=y;V.Writable=z;V.Duplex=B;V.Transform=Q;V.PassThrough=la;V.Stream=V;V.prototype.pipe=function(c,a){function b(a){c.writable&&!1===c.write(a)&&l.pause&&l.pause()}function d(){l.readable&&l.resume&&l.resume()}function e(){k||(k=!0,c.end())}function f(){k||(k=!0,"function"===typeof c.destroy&&c.destroy())}
function g(c){h();if(0===t.listenerCount(this,"error"))throw c;}function h(){l.removeListener("data",b);c.removeListener("drain",d);l.removeListener("end",e);l.removeListener("close",f);l.removeListener("error",g);c.removeListener("error",g);l.removeListener("end",h);l.removeListener("close",h);c.removeListener("close",h)}var l=this;l.on("data",b);c.on("drain",d);c._isStdio||a&&!1===a.end||(l.on("end",e),l.on("close",f));var k=!1;l.on("error",g);c.on("error",g);l.on("end",h);l.on("close",h);c.on("close",
h);c.emit("pipe",l);return c};var de=Array.prototype.slice,ee=function d(a,b){for(var e in b)a[e]=b[e];return 3>arguments.length?a:d.apply(null,[a].concat(de.call(arguments,2)))},Sc=E(function(a,b){function d(a,b,d){void 0===d&&(d=function(a){return a});return function(){for(var f=[],g=0;g<arguments.length;g++)f[g]=arguments[g];return new Promise(function(g,h){a[b].bind(a).apply(void 0,e(f,[function(a,b){return a?h(a):g(d(b))}]))})}}var e=H&&H.__spreadArrays||function(){for(var a=0,b=0,d=arguments.length;b<
d;b++)a+=arguments[b].length;a=Array(a);var e=0;for(b=0;b<d;b++)for(var f=arguments[b],k=0,r=f.length;k<r;k++,e++)a[e]=f[k];return a};Object.defineProperty(b,"__esModule",{value:!0});var f=function(){function a(a,b){this.vol=a;this.fd=b}a.prototype.appendFile=function(a,b){return d(this.vol,"appendFile")(this.fd,a,b)};a.prototype.chmod=function(a){return d(this.vol,"fchmod")(this.fd,a)};a.prototype.chown=function(a,b){return d(this.vol,"fchown")(this.fd,a,b)};a.prototype.close=function(){return d(this.vol,
"close")(this.fd)};a.prototype.datasync=function(){return d(this.vol,"fdatasync")(this.fd)};a.prototype.read=function(a,b,e,f){return d(this.vol,"read",function(b){return{bytesRead:b,buffer:a}})(this.fd,a,b,e,f)};a.prototype.readFile=function(a){return d(this.vol,"readFile")(this.fd,a)};a.prototype.stat=function(a){return d(this.vol,"fstat")(this.fd,a)};a.prototype.sync=function(){return d(this.vol,"fsync")(this.fd)};a.prototype.truncate=function(a){return d(this.vol,"ftruncate")(this.fd,a)};a.prototype.utimes=
function(a,b){return d(this.vol,"futimes")(this.fd,a,b)};a.prototype.write=function(a,b,e,f){return d(this.vol,"write",function(b){return{bytesWritten:b,buffer:a}})(this.fd,a,b,e,f)};a.prototype.writeFile=function(a,b){return d(this.vol,"writeFile")(this.fd,a,b)};return a}();b.FileHandle=f;b.default=function(a){return"undefined"===typeof Promise?null:{FileHandle:f,access:function(b,e){return d(a,"access")(b,e)},appendFile:function(b,e,g){return d(a,"appendFile")(b instanceof f?b.fd:b,e,g)},chmod:function(b,
e){return d(a,"chmod")(b,e)},chown:function(b,e,f){return d(a,"chown")(b,e,f)},copyFile:function(b,e,f){return d(a,"copyFile")(b,e,f)},lchmod:function(b,e){return d(a,"lchmod")(b,e)},lchown:function(b,e,f){return d(a,"lchown")(b,e,f)},link:function(b,e){return d(a,"link")(b,e)},lstat:function(b,e){return d(a,"lstat")(b,e)},mkdir:function(b,e){return d(a,"mkdir")(b,e)},mkdtemp:function(b,e){return d(a,"mkdtemp")(b,e)},open:function(b,e,g){return d(a,"open",function(b){return new f(a,b)})(b,e,g)},readdir:function(b,
e){return d(a,"readdir")(b,e)},readFile:function(b,e){return d(a,"readFile")(b instanceof f?b.fd:b,e)},readlink:function(b,e){return d(a,"readlink")(b,e)},realpath:function(b,e){return d(a,"realpath")(b,e)},rename:function(b,e){return d(a,"rename")(b,e)},rmdir:function(b){return d(a,"rmdir")(b)},stat:function(b,e){return d(a,"stat")(b,e)},symlink:function(b,e,f){return d(a,"symlink")(b,e,f)},truncate:function(b,e){return d(a,"truncate")(b,e)},unlink:function(b){return d(a,"unlink")(b)},utimes:function(b,
e,f){return d(a,"utimes")(b,e,f)},writeFile:function(b,e,g){return d(a,"writeFile")(b instanceof f?b.fd:b,e,g)}}}});D(Sc);var Od=/[^\x20-\x7E]/,Md=/[\x2E\u3002\uFF0E\uFF61]/g,Hc={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},sa=Math.floor,tb=String.fromCharCode,Jc=Array.isArray||function(a){return"[object Array]"===Object.prototype.toString.call(a)},Qd=Object.keys||function(a){var b=[],d;for(d in a)Object.prototype.hasOwnProperty.call(a,
d)&&b.push(d);return b},fe={parse:Xa,resolve:function(a,b){return Xa(a,!1,!0).resolve(b)},resolveObject:function(a,b){return a?Xa(a,!1,!0).resolveObject(b):b},format:function(a){ha(a)&&(a=Lc({},a));return wb(a)},Url:K},Sd=/^([a-z0-9.+-]+:)/i,Wd=/:[0-9]*$/,Rd=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,ge="{}|\\^`".split("").concat('<>"` \r\n\t'.split("")),vb=["'"].concat(ge),Nc=["%","/","?",";","#"].concat(vb),Mc=["/","?","#"],Ud=255,Pc=/^[+a-z0-9A-Z_-]{0,63}$/,Td=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Vd={javascript:!0,
"javascript:":!0},ub={javascript:!0,"javascript:":!0},ua={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};K.prototype.parse=function(a,b,d){return Lc(this,a,b,d)};K.prototype.format=function(){return wb(this)};K.prototype.resolve=function(a){return this.resolveObject(Xa(a,!1,!0)).format()};K.prototype.resolveObject=function(a){if(ha(a)){var b=new K;b.parse(a,!1,!0);a=b}b=new K;for(var d=Object.keys(this),e=0;e<d.length;e++){var f=d[e];b[f]=this[f]}b.hash=
a.hash;if(""===a.href)return b.href=b.format(),b;if(a.slashes&&!a.protocol){d=Object.keys(a);for(e=0;e<d.length;e++)f=d[e],"protocol"!==f&&(b[f]=a[f]);ua[b.protocol]&&b.hostname&&!b.pathname&&(b.path=b.pathname="/");b.href=b.format();return b}var g;if(a.protocol&&a.protocol!==b.protocol){if(!ua[a.protocol]){d=Object.keys(a);for(e=0;e<d.length;e++)f=d[e],b[f]=a[f];b.href=b.format();return b}b.protocol=a.protocol;if(a.host||ub[a.protocol])b.pathname=a.pathname;else{for(g=(a.pathname||"").split("/");g.length&&
!(a.host=g.shift()););a.host||(a.host="");a.hostname||(a.hostname="");""!==g[0]&&g.unshift("");2>g.length&&g.unshift("");b.pathname=g.join("/")}b.search=a.search;b.query=a.query;b.host=a.host||"";b.auth=a.auth;b.hostname=a.hostname||a.host;b.port=a.port;if(b.pathname||b.search)b.path=(b.pathname||"")+(b.search||"");b.slashes=b.slashes||a.slashes;b.href=b.format();return b}d=b.pathname&&"/"===b.pathname.charAt(0);var h=a.host||a.pathname&&"/"===a.pathname.charAt(0),l=d=h||d||b.host&&a.pathname;e=b.pathname&&
b.pathname.split("/")||[];f=b.protocol&&!ua[b.protocol];g=a.pathname&&a.pathname.split("/")||[];f&&(b.hostname="",b.port=null,b.host&&(""===e[0]?e[0]=b.host:e.unshift(b.host)),b.host="",a.protocol&&(a.hostname=null,a.port=null,a.host&&(""===g[0]?g[0]=a.host:g.unshift(a.host)),a.host=null),d=d&&(""===g[0]||""===e[0]));if(h)b.host=a.host||""===a.host?a.host:b.host,b.hostname=a.hostname||""===a.hostname?a.hostname:b.hostname,b.search=a.search,b.query=a.query,e=g;else if(g.length)e||(e=[]),e.pop(),e=
e.concat(g),b.search=a.search,b.query=a.query;else if(null!=a.search){f&&(b.hostname=b.host=e.shift(),f=b.host&&0<b.host.indexOf("@")?b.host.split("@"):!1)&&(b.auth=f.shift(),b.host=b.hostname=f.shift());b.search=a.search;b.query=a.query;if(null!==b.pathname||null!==b.search)b.path=(b.pathname?b.pathname:"")+(b.search?b.search:"");b.href=b.format();return b}if(!e.length)return b.pathname=null,b.path=b.search?"/"+b.search:null,b.href=b.format(),b;h=e.slice(-1)[0];g=(b.host||a.host||1<e.length)&&("."===
h||".."===h)||""===h;for(var k=0,m=e.length;0<=m;m--)h=e[m],"."===h?e.splice(m,1):".."===h?(e.splice(m,1),k++):k&&(e.splice(m,1),k--);if(!d&&!l)for(;k--;k)e.unshift("..");!d||""===e[0]||e[0]&&"/"===e[0].charAt(0)||e.unshift("");g&&"/"!==e.join("/").substr(-1)&&e.push("");l=""===e[0]||e[0]&&"/"===e[0].charAt(0);f&&(b.hostname=b.host=l?"":e.length?e.shift():"",f=b.host&&0<b.host.indexOf("@")?b.host.split("@"):!1)&&(b.auth=f.shift(),b.host=b.hostname=f.shift());(d=d||b.host&&e.length)&&!l&&e.unshift("");
e.length?b.pathname=e.join("/"):(b.pathname=null,b.path=null);if(null!==b.pathname||null!==b.search)b.path=(b.pathname?b.pathname:"")+(b.search?b.search:"");b.auth=a.auth||b.auth;b.slashes=b.slashes||a.slashes;b.href=b.format();return b};K.prototype.parseHost=function(){return Oc(this)};var Tc=E(function(a,b){function d(a,b){a=a[b];return 0<b&&("/"===a||f&&"\\"===a)}function e(a){var b=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0;if(f){var e=a;if("string"!==typeof e)throw new TypeError("expected a string");
e=e.replace(/[\\\/]+/g,"/");if(!1!==b)if(b=e,e=b.length-1,2>e)e=b;else{for(;d(b,e);)e--;e=b.substr(0,e+1)}return e.replace(/^([a-zA-Z]+:|\.\/)/,"")}return a}Object.defineProperty(b,"__esModule",{value:!0});b.unixify=e;b.correctPath=function(a){return e(a.replace(/^\\\\\?\\.:\\/,"\\"))};var f="win32"===pa.platform});D(Tc);var na=E(function(a,b){function d(a,b,d,e){void 0===b&&(b="");void 0===d&&(d="");void 0===e&&(e="");var n="";d&&(n=" '"+d+"'");e&&(n+=" -> '"+e+"'");switch(a){case "ENOENT":return"ENOENT: no such file or directory, "+
b+n;case "EBADF":return"EBADF: bad file descriptor, "+b+n;case "EINVAL":return"EINVAL: invalid argument, "+b+n;case "EPERM":return"EPERM: operation not permitted, "+b+n;case "EPROTO":return"EPROTO: protocol error, "+b+n;case "EEXIST":return"EEXIST: file already exists, "+b+n;case "ENOTDIR":return"ENOTDIR: not a directory, "+b+n;case "EISDIR":return"EISDIR: illegal operation on a directory, "+b+n;case "EACCES":return"EACCES: permission denied, "+b+n;case "ENOTEMPTY":return"ENOTEMPTY: directory not empty, "+
b+n;case "EMFILE":return"EMFILE: too many open files, "+b+n;case "ENOSYS":return"ENOSYS: function not implemented, "+b+n;default:return a+": error occurred, "+b+n}}function e(a,b,e,L,f){void 0===b&&(b="");void 0===e&&(e="");void 0===L&&(L="");void 0===f&&(f=Error);b=new f(d(a,b,e,L));b.code=a;return b}function f(a){if("number"===typeof a)return a;if("string"===typeof a){var b=N[a];if("undefined"!==typeof b)return b}throw new Ma.TypeError("ERR_INVALID_OPT_VALUE","flags",a);}function g(a,b){if(b){var d=
typeof b;switch(d){case "string":a=G({},a,{encoding:b});break;case "object":a=G({},a,b);break;default:throw TypeError("Expected options to be either an object or a string, but got "+d+" instead");}}else return a;"buffer"!==a.encoding&&Y.assertEncoding(a.encoding);return a}function h(a){return function(b){return g(a,b)}}function l(a){if("function"!==typeof a)throw TypeError(wa.CB);return a}function k(a){return function(b,d){return"function"===typeof b?[a(),b]:[a(b),l(d)]}}function m(a){if("string"!==
typeof a&&!C.Buffer.isBuffer(a)){try{if(!(a instanceof fe.URL))throw new TypeError(wa.PATH_STR);}catch(L){throw new TypeError(wa.PATH_STR);}if(""!==a.hostname)throw new Ma.TypeError("ERR_INVALID_FILE_URL_HOST",R.default.platform);a=a.pathname;for(var b=0;b<a.length;b++)if("%"===a[b]){var d=a.codePointAt(b+2)|32;if("2"===a[b+1]&&102===d)throw new Ma.TypeError("ERR_INVALID_FILE_URL_PATH","must not include encoded / characters");}a=decodeURIComponent(a)}a=String(a);y(a);return a}function p(a,b){return(a=
la(a,b).substr(1))?a.split(da):[]}function v(a){return p(m(a))}function x(a,b){void 0===b&&(b=Y.ENCODING_UTF8);return C.Buffer.isBuffer(a)?a:a instanceof Uint8Array?C.bufferFrom(a):C.bufferFrom(String(a),b)}function ta(a,b){return b&&"buffer"!==b?a.toString(b):a}function y(a,b){if(-1!==(""+a).indexOf("\x00")){a=Error("Path must be a string without null bytes");a.code="ENOENT";if("function"!==typeof b)throw a;R.default.nextTick(b,a);return!1}return!0}function w(a,b){a="number"===typeof a?a:"string"===
typeof a?parseInt(a,8):b?w(b):void 0;if("number"!==typeof a||isNaN(a))throw new TypeError(wa.MODE_INT);return a}function E(a){if(a>>>0!==a)throw TypeError(wa.FD);}function z(a){if("string"===typeof a&&+a==a)return+a;if(a instanceof Date)return a.getTime()/1E3;if(isFinite(a))return 0>a?Date.now()/1E3:a;throw Error("Cannot parse time: "+a);}function D(a){if("number"!==typeof a)throw TypeError(wa.UID);}function F(a){if("number"!==typeof a)throw TypeError(wa.GID);}function M(a){a.emit("stop")}function A(a,
b,d){if(!(this instanceof A))return new A(a,b,d);this._vol=a;d=G({},g(d,{}));void 0===d.highWaterMark&&(d.highWaterMark=65536);V.Readable.call(this,d);this.path=m(b);this.fd=void 0===d.fd?null:d.fd;this.flags=void 0===d.flags?"r":d.flags;this.mode=void 0===d.mode?438:d.mode;this.start=d.start;this.end=d.end;this.autoClose=void 0===d.autoClose?!0:d.autoClose;this.pos=void 0;this.bytesRead=0;if(void 0!==this.start){if("number"!==typeof this.start)throw new TypeError('"start" option must be a Number');
if(void 0===this.end)this.end=Infinity;else if("number"!==typeof this.end)throw new TypeError('"end" option must be a Number');if(this.start>this.end)throw Error('"start" option must be <= "end" option');this.pos=this.start}"number"!==typeof this.fd&&this.open();this.on("end",function(){this.autoClose&&this.destroy&&this.destroy()})}function S(a){this.close()}function B(a,b,d){if(!(this instanceof B))return new B(a,b,d);this._vol=a;d=G({},g(d,{}));V.Writable.call(this,d);this.path=m(b);this.fd=void 0===
d.fd?null:d.fd;this.flags=void 0===d.flags?"w":d.flags;this.mode=void 0===d.mode?438:d.mode;this.start=d.start;this.autoClose=void 0===d.autoClose?!0:!!d.autoClose;this.pos=void 0;this.bytesWritten=0;if(void 0!==this.start){if("number"!==typeof this.start)throw new TypeError('"start" option must be a Number');if(0>this.start)throw Error('"start" must be >= zero');this.pos=this.start}d.encoding&&this.setDefaultEncoding(d.encoding);"number"!==typeof this.fd&&this.open();this.once("finish",function(){this.autoClose&&
this.close()})}var J=H&&H.__extends||function(){var a=function(b,d){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var d in b)b.hasOwnProperty(d)&&(a[d]=b[d])};return a(b,d)};return function(b,d){function e(){this.constructor=b}a(b,d);b.prototype=null===d?Object.create(d):(e.prototype=d.prototype,new e)}}(),T=H&&H.__spreadArrays||function(){for(var a=0,b=0,d=arguments.length;b<d;b++)a+=arguments[b].length;a=Array(a);var e=0;for(b=0;b<d;b++)for(var f=
arguments[b],g=0,h=f.length;g<h;g++,e++)a[e]=f[g];return a};Object.defineProperty(b,"__esModule",{value:!0});var G=ee,Q=r.constants.O_RDONLY,K=r.constants.O_WRONLY,I=r.constants.O_RDWR,ea=r.constants.O_CREAT,U=r.constants.O_EXCL,O=r.constants.O_TRUNC,P=r.constants.O_APPEND,aa=r.constants.O_SYNC,fa=r.constants.O_DIRECTORY,ba=r.constants.F_OK,ja=r.constants.COPYFILE_EXCL,ma=r.constants.COPYFILE_FICLONE_FORCE;var da="/";var Z="win32"===R.default.platform,wa={PATH_STR:"path must be a string or Buffer",
FD:"fd must be a file descriptor",MODE_INT:"mode must be an int",CB:"callback must be a function",UID:"uid must be an unsigned int",GID:"gid must be an unsigned int",LEN:"len must be an integer",ATIME:"atime must be an integer",MTIME:"mtime must be an integer",PREFIX:"filename prefix is required",BUFFER:"buffer must be an instance of Buffer or StaticBuffer",OFFSET:"offset must be an integer",LENGTH:"length must be an integer",POSITION:"position must be an integer"},N;(function(a){a[a.r=Q]="r";a[a["r+"]=
I]="r+";a[a.rs=Q|aa]="rs";a[a.sr=a.rs]="sr";a[a["rs+"]=I|aa]="rs+";a[a["sr+"]=a["rs+"]]="sr+";a[a.w=K|ea|O]="w";a[a.wx=K|ea|O|U]="wx";a[a.xw=a.wx]="xw";a[a["w+"]=I|ea|O]="w+";a[a["wx+"]=I|ea|O|U]="wx+";a[a["xw+"]=a["wx+"]]="xw+";a[a.a=K|P|ea]="a";a[a.ax=K|P|ea|U]="ax";a[a.xa=a.ax]="xa";a[a["a+"]=I|P|ea]="a+";a[a["ax+"]=I|P|ea|U]="ax+";a[a["xa+"]=a["ax+"]]="xa+"})(N=b.FLAGS||(b.FLAGS={}));b.flagsToNumber=f;a={encoding:"utf8"};var X=h(a),ha=k(X),ia=h({flag:"r"}),ka={encoding:"utf8",mode:438,flag:N[N.w]},
na=h(ka),oa={encoding:"utf8",mode:438,flag:N[N.a]},pa=h(oa),za=k(pa),qa=h(a),Aa=k(qa),sa={mode:511,recursive:!1},ua=function(a){return"number"===typeof a?G({},sa,{mode:a}):G({},sa,a)},va={recursive:!1},ya=h({encoding:"utf8",withFileTypes:!1}),Da=k(ya),Ea={bigint:!1},ca=function(a){void 0===a&&(a={});return G({},Ea,a)},ra=function(a,b){return"function"===typeof a?[ca(),a]:[ca(a),l(b)]};b.pathToFilename=m;var la=function(a,b){void 0===b&&(b=R.default.cwd());return ob(b,a)};if(Z){var Fa=la,Ga=Tc.unixify;
la=function(a,b){return Ga(Fa(a,b))}}b.filenameToSteps=p;b.pathToSteps=v;b.dataToStr=function(a,b){void 0===b&&(b=Y.ENCODING_UTF8);return C.Buffer.isBuffer(a)?a.toString(b):a instanceof Uint8Array?C.bufferFrom(a).toString(b):String(a)};b.dataToBuffer=x;b.bufferToEncoding=ta;b.toUnixTimestamp=z;a=function(){function a(a){void 0===a&&(a={});this.ino=0;this.inodes={};this.releasedInos=[];this.fds={};this.releasedFds=[];this.maxFiles=1E4;this.openFiles=0;this.promisesApi=Sc.default(this);this.statWatchers=
{};this.props=G({Node:Na.Node,Link:Na.Link,File:Na.File},a);a=this.createLink();a.setNode(this.createNode(!0));var b=this;this.StatWatcher=function(a){function d(){return a.call(this,b)||this}J(d,a);return d}(Ba);this.ReadStream=function(a){function d(){for(var d=[],e=0;e<arguments.length;e++)d[e]=arguments[e];return a.apply(this,T([b],d))||this}J(d,a);return d}(A);this.WriteStream=function(a){function d(){for(var d=[],e=0;e<arguments.length;e++)d[e]=arguments[e];return a.apply(this,T([b],d))||this}
J(d,a);return d}(B);this.FSWatcher=function(a){function d(){return a.call(this,b)||this}J(d,a);return d}(Ca);this.root=a}a.fromJSON=function(b,d){var e=new a;e.fromJSON(b,d);return e};Object.defineProperty(a.prototype,"promises",{get:function(){if(null===this.promisesApi)throw Error("Promise is not supported in this environment.");return this.promisesApi},enumerable:!0,configurable:!0});a.prototype.createLink=function(a,b,d,e){void 0===d&&(d=!1);if(!a)return new this.props.Link(this,null,"");if(!b)throw Error("createLink: name cannot be empty");
return a.createChild(b,this.createNode(d,e))};a.prototype.deleteLink=function(a){var b=a.parent;return b?(b.deleteChild(a),!0):!1};a.prototype.newInoNumber=function(){var a=this.releasedInos.pop();return a?a:this.ino=(this.ino+1)%4294967295};a.prototype.newFdNumber=function(){var b=this.releasedFds.pop();return"number"===typeof b?b:a.fd--};a.prototype.createNode=function(a,b){void 0===a&&(a=!1);b=new this.props.Node(this.newInoNumber(),b);a&&b.setIsDirectory();return this.inodes[b.ino]=b};a.prototype.getNode=
function(a){return this.inodes[a]};a.prototype.deleteNode=function(a){a.del();delete this.inodes[a.ino];this.releasedInos.push(a.ino)};a.prototype.genRndStr=function(){var a=(Math.random()+1).toString(36).substr(2,6);return 6===a.length?a:this.genRndStr()};a.prototype.getLink=function(a){return this.root.walk(a)};a.prototype.getLinkOrThrow=function(a,b){var d=p(a);d=this.getLink(d);if(!d)throw e("ENOENT",b,a);return d};a.prototype.getResolvedLink=function(a){a="string"===typeof a?p(a):a;for(var b=
this.root,d=0;d<a.length;){b=b.getChild(a[d]);if(!b)return null;var e=b.getNode();e.isSymlink()?(a=e.symlink.concat(a.slice(d+1)),b=this.root,d=0):d++}return b};a.prototype.getResolvedLinkOrThrow=function(a,b){var d=this.getResolvedLink(a);if(!d)throw e("ENOENT",b,a);return d};a.prototype.resolveSymlinks=function(a){return this.getResolvedLink(a.steps.slice(1))};a.prototype.getLinkAsDirOrThrow=function(a,b){var d=this.getLinkOrThrow(a,b);if(!d.getNode().isDirectory())throw e("ENOTDIR",b,a);return d};
a.prototype.getLinkParent=function(a){return this.root.walk(a,a.length-1)};a.prototype.getLinkParentAsDirOrThrow=function(a,b){a=a instanceof Array?a:p(a);var d=this.getLinkParent(a);if(!d)throw e("ENOENT",b,da+a.join(da));if(!d.getNode().isDirectory())throw e("ENOTDIR",b,da+a.join(da));return d};a.prototype.getFileByFd=function(a){return this.fds[String(a)]};a.prototype.getFileByFdOrThrow=function(a,b){if(a>>>0!==a)throw TypeError(wa.FD);a=this.getFileByFd(a);if(!a)throw e("EBADF",b);return a};a.prototype.getNodeByIdOrCreate=
function(a,b,d){if("number"===typeof a){a=this.getFileByFd(a);if(!a)throw Error("File nto found");return a.node}var n=v(a),q=this.getLink(n);if(q)return q.getNode();if(b&ea&&(b=this.getLinkParent(n)))return q=this.createLink(b,n[n.length-1],!1,d),q.getNode();throw e("ENOENT","getNodeByIdOrCreate",m(a));};a.prototype.wrapAsync=function(a,b,d){var e=this;l(d);Ja.default(function(){try{d(null,a.apply(e,b))}catch(xa){d(xa)}})};a.prototype._toJSON=function(a,b,d){var e;void 0===a&&(a=this.root);void 0===
b&&(b={});var n=!0,q=a.children;a.getNode().isFile()&&(q=(e={},e[a.getName()]=a.parent.getChild(a.getName()),e),a=a.parent);for(var L in q){n=!1;q=a.getChild(L);if(!q)throw Error("_toJSON: unexpected undefined");e=q.getNode();e.isFile()?(q=q.getPath(),d&&(q=Va(d,q)),b[q]=e.getString()):e.isDirectory()&&this._toJSON(q,b,d)}a=a.getPath();d&&(a=Va(d,a));a&&n&&(b[a]=null);return b};a.prototype.toJSON=function(a,b,d){void 0===b&&(b={});void 0===d&&(d=!1);var e=[];if(a){a instanceof Array||(a=[a]);for(var n=
0;n<a.length;n++){var q=m(a[n]);(q=this.getResolvedLink(q))&&e.push(q)}}else e.push(this.root);if(!e.length)return b;for(n=0;n<e.length;n++)q=e[n],this._toJSON(q,b,d?q.getPath():"");return b};a.prototype.fromJSON=function(a,b){void 0===b&&(b=R.default.cwd());for(var d in a){var e=a[d];if("string"===typeof e){d=la(d,b);var n=p(d);1<n.length&&(n=da+n.slice(0,n.length-1).join(da),this.mkdirpBase(n,511));this.writeFileSync(d,e)}else this.mkdirpBase(d,511)}};a.prototype.reset=function(){this.ino=0;this.inodes=
{};this.releasedInos=[];this.fds={};this.releasedFds=[];this.openFiles=0;this.root=this.createLink();this.root.setNode(this.createNode(!0))};a.prototype.mountSync=function(a,b){this.fromJSON(b,a)};a.prototype.openLink=function(a,b,d){void 0===d&&(d=!0);if(this.openFiles>=this.maxFiles)throw e("EMFILE","open",a.getPath());var n=a;d&&(n=this.resolveSymlinks(a));if(!n)throw e("ENOENT","open",a.getPath());d=n.getNode();if(d.isDirectory()){if((b&(Q|I|K))!==Q)throw e("EISDIR","open",a.getPath());}else if(b&
fa)throw e("ENOTDIR","open",a.getPath());if(!(b&K||d.canRead()))throw e("EACCES","open",a.getPath());a=new this.props.File(a,d,b,this.newFdNumber());this.fds[a.fd]=a;this.openFiles++;b&O&&a.truncate();return a};a.prototype.openFile=function(a,b,d,f){void 0===f&&(f=!0);var n=p(a),q=f?this.getResolvedLink(n):this.getLink(n);if(!q&&b&ea){var L=this.getResolvedLink(n.slice(0,n.length-1));if(!L)throw e("ENOENT","open",da+n.join(da));b&ea&&"number"===typeof d&&(q=this.createLink(L,n[n.length-1],!1,d))}if(q)return this.openLink(q,
b,f);throw e("ENOENT","open",a);};a.prototype.openBase=function(a,b,d,f){void 0===f&&(f=!0);b=this.openFile(a,b,d,f);if(!b)throw e("ENOENT","open",a);return b.fd};a.prototype.openSync=function(a,b,d){void 0===d&&(d=438);d=w(d);a=m(a);b=f(b);return this.openBase(a,b,d)};a.prototype.open=function(a,b,d,e){var n=d;"function"===typeof d&&(n=438,e=d);d=w(n||438);a=m(a);b=f(b);this.wrapAsync(this.openBase,[a,b,d],e)};a.prototype.closeFile=function(a){this.fds[a.fd]&&(this.openFiles--,delete this.fds[a.fd],
this.releasedFds.push(a.fd))};a.prototype.closeSync=function(a){E(a);a=this.getFileByFdOrThrow(a,"close");this.closeFile(a)};a.prototype.close=function(a,b){E(a);this.wrapAsync(this.closeSync,[a],b)};a.prototype.openFileOrGetById=function(a,b,d){if("number"===typeof a){a=this.fds[a];if(!a)throw e("ENOENT");return a}return this.openFile(m(a),b,d)};a.prototype.readBase=function(a,b,d,e,f){return this.getFileByFdOrThrow(a).read(b,Number(d),Number(e),f)};a.prototype.readSync=function(a,b,d,e,f){E(a);
return this.readBase(a,b,d,e,f)};a.prototype.read=function(a,b,d,e,f,g){var n=this;l(g);if(0===e)return R.default.nextTick(function(){g&&g(null,0,b)});Ja.default(function(){try{var q=n.readBase(a,b,d,e,f);g(null,q,b)}catch(he){g(he)}})};a.prototype.readFileBase=function(a,b,d){var n="number"===typeof a&&a>>>0===a;if(!n){var q=m(a);q=p(q);if((q=this.getResolvedLink(q))&&q.getNode().isDirectory())throw e("EISDIR","open",q.getPath());a=this.openSync(a,b)}try{var f=ta(this.getFileByFdOrThrow(a).getBuffer(),
d)}finally{n||this.closeSync(a)}return f};a.prototype.readFileSync=function(a,b){b=ia(b);var d=f(b.flag);return this.readFileBase(a,d,b.encoding)};a.prototype.readFile=function(a,b,d){d=k(ia)(b,d);b=d[0];d=d[1];var e=f(b.flag);this.wrapAsync(this.readFileBase,[a,e,b.encoding],d)};a.prototype.writeBase=function(a,b,d,e,f){return this.getFileByFdOrThrow(a,"write").write(b,d,e,f)};a.prototype.writeSync=function(a,b,d,e,f){E(a);var n="string"!==typeof b;if(n){var q=(d||0)|0;var L=e;d=f}else var g=e;b=
x(b,g);n?"undefined"===typeof L&&(L=b.length):(q=0,L=b.length);return this.writeBase(a,b,q,L,d)};a.prototype.write=function(a,b,d,e,f,g){var n=this;E(a);var q=typeof b,L=typeof d,h=typeof e,k=typeof f;if("string"!==q)if("function"===L)var Ha=d;else if("function"===h){var m=d|0;Ha=e}else if("function"===k){m=d|0;var p=e;Ha=f}else{m=d|0;p=e;var xa=f;Ha=g}else if("function"===L)Ha=d;else if("function"===h)xa=d,Ha=e;else if("function"===k){xa=d;var u=e;Ha=f}var r=x(b,u);"string"!==q?"undefined"===typeof p&&
(p=r.length):(m=0,p=r.length);var t=l(Ha);Ja.default(function(){try{var d=n.writeBase(a,r,m,p,xa);"string"!==q?t(null,d,r):t(null,d,b)}catch(ie){t(ie)}})};a.prototype.writeFileBase=function(a,b,d,e){var n="number"===typeof a;a=n?a:this.openBase(m(a),d,e);e=0;var q=b.length;d=d&P?void 0:0;try{for(;0<q;){var f=this.writeSync(a,b,e,q,d);e+=f;q-=f;void 0!==d&&(d+=f)}}finally{n||this.closeSync(a)}};a.prototype.writeFileSync=function(a,b,d){var e=na(d);d=f(e.flag);var n=w(e.mode);b=x(b,e.encoding);this.writeFileBase(a,
b,d,n)};a.prototype.writeFile=function(a,b,d,e){var n=d;"function"===typeof d&&(n=ka,e=d);d=l(e);var q=na(n);n=f(q.flag);e=w(q.mode);b=x(b,q.encoding);this.wrapAsync(this.writeFileBase,[a,b,n,e],d)};a.prototype.linkBase=function(a,b){var d=p(a),n=this.getLink(d);if(!n)throw e("ENOENT","link",a,b);var q=p(b);d=this.getLinkParent(q);if(!d)throw e("ENOENT","link",a,b);q=q[q.length-1];if(d.getChild(q))throw e("EEXIST","link",a,b);a=n.getNode();a.nlink++;d.createChild(q,a)};a.prototype.copyFileBase=function(a,
b,d){var n=this.readFileSync(a);if(d&ja&&this.existsSync(b))throw e("EEXIST","copyFile",a,b);if(d&ma)throw e("ENOSYS","copyFile",a,b);this.writeFileBase(b,n,N.w,438)};a.prototype.copyFileSync=function(a,b,d){a=m(a);b=m(b);return this.copyFileBase(a,b,(d||0)|0)};a.prototype.copyFile=function(a,b,d,e){a=m(a);b=m(b);if("function"===typeof d)var n=0;else n=d,d=e;l(d);this.wrapAsync(this.copyFileBase,[a,b,n],d)};a.prototype.linkSync=function(a,b){a=m(a);b=m(b);this.linkBase(a,b)};a.prototype.link=function(a,
b,d){a=m(a);b=m(b);this.wrapAsync(this.linkBase,[a,b],d)};a.prototype.unlinkBase=function(a){var b=p(a);b=this.getLink(b);if(!b)throw e("ENOENT","unlink",a);if(b.length)throw Error("Dir not empty...");this.deleteLink(b);a=b.getNode();a.nlink--;0>=a.nlink&&this.deleteNode(a)};a.prototype.unlinkSync=function(a){a=m(a);this.unlinkBase(a)};a.prototype.unlink=function(a,b){a=m(a);this.wrapAsync(this.unlinkBase,[a],b)};a.prototype.symlinkBase=function(a,b){var d=p(b),n=this.getLinkParent(d);if(!n)throw e("ENOENT",
"symlink",a,b);d=d[d.length-1];if(n.getChild(d))throw e("EEXIST","symlink",a,b);b=n.createChild(d);b.getNode().makeSymlink(p(a));return b};a.prototype.symlinkSync=function(a,b,d){a=m(a);b=m(b);this.symlinkBase(a,b)};a.prototype.symlink=function(a,b,d,e){d=l("function"===typeof d?d:e);a=m(a);b=m(b);this.wrapAsync(this.symlinkBase,[a,b],d)};a.prototype.realpathBase=function(a,b){var d=p(a);d=this.getResolvedLink(d);if(!d)throw e("ENOENT","realpath",a);return Y.strToEncoding(d.getPath(),b)};a.prototype.realpathSync=
function(a,b){return this.realpathBase(m(a),qa(b).encoding)};a.prototype.realpath=function(a,b,d){d=Aa(b,d);b=d[0];d=d[1];a=m(a);this.wrapAsync(this.realpathBase,[a,b.encoding],d)};a.prototype.lstatBase=function(a,b){void 0===b&&(b=!1);var d=this.getLink(p(a));if(!d)throw e("ENOENT","lstat",a);return Ia.default.build(d.getNode(),b)};a.prototype.lstatSync=function(a,b){return this.lstatBase(m(a),ca(b).bigint)};a.prototype.lstat=function(a,b,d){d=ra(b,d);b=d[0];d=d[1];this.wrapAsync(this.lstatBase,
[m(a),b.bigint],d)};a.prototype.statBase=function(a,b){void 0===b&&(b=!1);var d=this.getResolvedLink(p(a));if(!d)throw e("ENOENT","stat",a);return Ia.default.build(d.getNode(),b)};a.prototype.statSync=function(a,b){return this.statBase(m(a),ca(b).bigint)};a.prototype.stat=function(a,b,d){d=ra(b,d);b=d[0];d=d[1];this.wrapAsync(this.statBase,[m(a),b.bigint],d)};a.prototype.fstatBase=function(a,b){void 0===b&&(b=!1);a=this.getFileByFd(a);if(!a)throw e("EBADF","fstat");return Ia.default.build(a.node,
b)};a.prototype.fstatSync=function(a,b){return this.fstatBase(a,ca(b).bigint)};a.prototype.fstat=function(a,b,d){b=ra(b,d);this.wrapAsync(this.fstatBase,[a,b[0].bigint],b[1])};a.prototype.renameBase=function(a,b){var d=this.getLink(p(a));if(!d)throw e("ENOENT","rename",a,b);var f=p(b),n=this.getLinkParent(f);if(!n)throw e("ENOENT","rename",a,b);(a=d.parent)&&a.deleteChild(d);d.steps=T(n.steps,[f[f.length-1]]);n.setChild(d.getName(),d)};a.prototype.renameSync=function(a,b){a=m(a);b=m(b);this.renameBase(a,
b)};a.prototype.rename=function(a,b,d){a=m(a);b=m(b);this.wrapAsync(this.renameBase,[a,b],d)};a.prototype.existsBase=function(a){return!!this.statBase(a)};a.prototype.existsSync=function(a){try{return this.existsBase(m(a))}catch(q){return!1}};a.prototype.exists=function(a,b){var d=this,e=m(a);if("function"!==typeof b)throw Error(wa.CB);Ja.default(function(){try{b(d.existsBase(e))}catch(xa){b(!1)}})};a.prototype.accessBase=function(a,b){this.getLinkOrThrow(a,"access")};a.prototype.accessSync=function(a,
b){void 0===b&&(b=ba);a=m(a);this.accessBase(a,b|0)};a.prototype.access=function(a,b,d){var e=ba;"function"!==typeof b&&(e=b|0,b=l(d));a=m(a);this.wrapAsync(this.accessBase,[a,e],b)};a.prototype.appendFileSync=function(a,b,d){void 0===d&&(d=oa);d=pa(d);d.flag&&a>>>0!==a||(d.flag="a");this.writeFileSync(a,b,d)};a.prototype.appendFile=function(a,b,d,e){e=za(d,e);d=e[0];e=e[1];d.flag&&a>>>0!==a||(d.flag="a");this.writeFile(a,b,d,e)};a.prototype.readdirBase=function(a,b){var d=p(a);d=this.getResolvedLink(d);
if(!d)throw e("ENOENT","readdir",a);if(!d.getNode().isDirectory())throw e("ENOTDIR","scandir",a);if(b.withFileTypes){var f=[];for(n in d.children)(a=d.getChild(n))&&f.push(yb.default.build(a,b.encoding));Z||"buffer"===b.encoding||f.sort(function(a,b){return a.name<b.name?-1:a.name>b.name?1:0});return f}var n=[];for(f in d.children)n.push(Y.strToEncoding(f,b.encoding));Z||"buffer"===b.encoding||n.sort();return n};a.prototype.readdirSync=function(a,b){b=ya(b);a=m(a);return this.readdirBase(a,b)};a.prototype.readdir=
function(a,b,d){d=Da(b,d);b=d[0];d=d[1];a=m(a);this.wrapAsync(this.readdirBase,[a,b],d)};a.prototype.readlinkBase=function(a,b){var d=this.getLinkOrThrow(a,"readlink").getNode();if(!d.isSymlink())throw e("EINVAL","readlink",a);a=da+d.symlink.join(da);return Y.strToEncoding(a,b)};a.prototype.readlinkSync=function(a,b){b=X(b);a=m(a);return this.readlinkBase(a,b.encoding)};a.prototype.readlink=function(a,b,d){d=ha(b,d);b=d[0];d=d[1];a=m(a);this.wrapAsync(this.readlinkBase,[a,b.encoding],d)};a.prototype.fsyncBase=
function(a){this.getFileByFdOrThrow(a,"fsync")};a.prototype.fsyncSync=function(a){this.fsyncBase(a)};a.prototype.fsync=function(a,b){this.wrapAsync(this.fsyncBase,[a],b)};a.prototype.fdatasyncBase=function(a){this.getFileByFdOrThrow(a,"fdatasync")};a.prototype.fdatasyncSync=function(a){this.fdatasyncBase(a)};a.prototype.fdatasync=function(a,b){this.wrapAsync(this.fdatasyncBase,[a],b)};a.prototype.ftruncateBase=function(a,b){this.getFileByFdOrThrow(a,"ftruncate").truncate(b)};a.prototype.ftruncateSync=
function(a,b){this.ftruncateBase(a,b)};a.prototype.ftruncate=function(a,b,d){var e="number"===typeof b?b:0;b=l("number"===typeof b?d:b);this.wrapAsync(this.ftruncateBase,[a,e],b)};a.prototype.truncateBase=function(a,b){a=this.openSync(a,"r+");try{this.ftruncateSync(a,b)}finally{this.closeSync(a)}};a.prototype.truncateSync=function(a,b){if(a>>>0===a)return this.ftruncateSync(a,b);this.truncateBase(a,b)};a.prototype.truncate=function(a,b,d){var e="number"===typeof b?b:0;b=l("number"===typeof b?d:b);
if(a>>>0===a)return this.ftruncate(a,e,b);this.wrapAsync(this.truncateBase,[a,e],b)};a.prototype.futimesBase=function(a,b,d){a=this.getFileByFdOrThrow(a,"futimes").node;a.atime=new Date(1E3*b);a.mtime=new Date(1E3*d)};a.prototype.futimesSync=function(a,b,d){this.futimesBase(a,z(b),z(d))};a.prototype.futimes=function(a,b,d,e){this.wrapAsync(this.futimesBase,[a,z(b),z(d)],e)};a.prototype.utimesBase=function(a,b,d){a=this.openSync(a,"r+");try{this.futimesBase(a,b,d)}finally{this.closeSync(a)}};a.prototype.utimesSync=
function(a,b,d){this.utimesBase(m(a),z(b),z(d))};a.prototype.utimes=function(a,b,d,e){this.wrapAsync(this.utimesBase,[m(a),z(b),z(d)],e)};a.prototype.mkdirBase=function(a,b){var d=p(a);if(!d.length)throw e("EISDIR","mkdir",a);var f=this.getLinkParentAsDirOrThrow(a,"mkdir");d=d[d.length-1];if(f.getChild(d))throw e("EEXIST","mkdir",a);f.createChild(d,this.createNode(!0,b))};a.prototype.mkdirpBase=function(a,b){a=p(a);for(var d=this.root,f=0;f<a.length;f++){var g=a[f];if(!d.getNode().isDirectory())throw e("ENOTDIR",
"mkdir",d.getPath());var n=d.getChild(g);if(n)if(n.getNode().isDirectory())d=n;else throw e("ENOTDIR","mkdir",n.getPath());else d=d.createChild(g,this.createNode(!0,b))}};a.prototype.mkdirSync=function(a,b){b=ua(b);var d=w(b.mode,511);a=m(a);b.recursive?this.mkdirpBase(a,d):this.mkdirBase(a,d)};a.prototype.mkdir=function(a,b,d){var e=ua(b);b=l("function"===typeof b?b:d);d=w(e.mode,511);a=m(a);e.recursive?this.wrapAsync(this.mkdirpBase,[a,d],b):this.wrapAsync(this.mkdirBase,[a,d],b)};a.prototype.mkdirpSync=
function(a,b){this.mkdirSync(a,{mode:b,recursive:!0})};a.prototype.mkdirp=function(a,b,d){var e="function"===typeof b?void 0:b;b=l("function"===typeof b?b:d);this.mkdir(a,{mode:e,recursive:!0},b)};a.prototype.mkdtempBase=function(a,b,d){void 0===d&&(d=5);var e=a+this.genRndStr();try{return this.mkdirBase(e,511),Y.strToEncoding(e,b)}catch(xa){if("EEXIST"===xa.code){if(1<d)return this.mkdtempBase(a,b,d-1);throw Error("Could not create temp dir.");}throw xa;}};a.prototype.mkdtempSync=function(a,b){b=
X(b).encoding;if(!a||"string"!==typeof a)throw new TypeError("filename prefix is required");y(a);return this.mkdtempBase(a,b)};a.prototype.mkdtemp=function(a,b,d){d=ha(b,d);b=d[0].encoding;d=d[1];if(!a||"string"!==typeof a)throw new TypeError("filename prefix is required");y(a)&&this.wrapAsync(this.mkdtempBase,[a,b],d)};a.prototype.rmdirBase=function(a,b){b=G({},va,b);var d=this.getLinkAsDirOrThrow(a,"rmdir");if(d.length&&!b.recursive)throw e("ENOTEMPTY","rmdir",a);this.deleteLink(d)};a.prototype.rmdirSync=
function(a,b){this.rmdirBase(m(a),b)};a.prototype.rmdir=function(a,b,d){var e=G({},va,b);b=l("function"===typeof b?b:d);this.wrapAsync(this.rmdirBase,[m(a),e],b)};a.prototype.fchmodBase=function(a,b){this.getFileByFdOrThrow(a,"fchmod").chmod(b)};a.prototype.fchmodSync=function(a,b){this.fchmodBase(a,w(b))};a.prototype.fchmod=function(a,b,d){this.wrapAsync(this.fchmodBase,[a,w(b)],d)};a.prototype.chmodBase=function(a,b){a=this.openSync(a,"r+");try{this.fchmodBase(a,b)}finally{this.closeSync(a)}};a.prototype.chmodSync=
function(a,b){b=w(b);a=m(a);this.chmodBase(a,b)};a.prototype.chmod=function(a,b,d){b=w(b);a=m(a);this.wrapAsync(this.chmodBase,[a,b],d)};a.prototype.lchmodBase=function(a,b){a=this.openBase(a,I,0,!1);try{this.fchmodBase(a,b)}finally{this.closeSync(a)}};a.prototype.lchmodSync=function(a,b){b=w(b);a=m(a);this.lchmodBase(a,b)};a.prototype.lchmod=function(a,b,d){b=w(b);a=m(a);this.wrapAsync(this.lchmodBase,[a,b],d)};a.prototype.fchownBase=function(a,b,d){this.getFileByFdOrThrow(a,"fchown").chown(b,d)};
a.prototype.fchownSync=function(a,b,d){D(b);F(d);this.fchownBase(a,b,d)};a.prototype.fchown=function(a,b,d,e){D(b);F(d);this.wrapAsync(this.fchownBase,[a,b,d],e)};a.prototype.chownBase=function(a,b,d){this.getResolvedLinkOrThrow(a,"chown").getNode().chown(b,d)};a.prototype.chownSync=function(a,b,d){D(b);F(d);this.chownBase(m(a),b,d)};a.prototype.chown=function(a,b,d,e){D(b);F(d);this.wrapAsync(this.chownBase,[m(a),b,d],e)};a.prototype.lchownBase=function(a,b,d){this.getLinkOrThrow(a,"lchown").getNode().chown(b,
d)};a.prototype.lchownSync=function(a,b,d){D(b);F(d);this.lchownBase(m(a),b,d)};a.prototype.lchown=function(a,b,d,e){D(b);F(d);this.wrapAsync(this.lchownBase,[m(a),b,d],e)};a.prototype.watchFile=function(a,b,d){a=m(a);var e=b;"function"===typeof e&&(d=b,e=null);if("function"!==typeof d)throw Error('"watchFile()" requires a listener function');b=5007;var f=!0;e&&"object"===typeof e&&("number"===typeof e.interval&&(b=e.interval),"boolean"===typeof e.persistent&&(f=e.persistent));e=this.statWatchers[a];
e||(e=new this.StatWatcher,e.start(a,f,b),this.statWatchers[a]=e);e.addListener("change",d);return e};a.prototype.unwatchFile=function(a,b){a=m(a);var d=this.statWatchers[a];d&&("function"===typeof b?d.removeListener("change",b):d.removeAllListeners("change"),0===d.listenerCount("change")&&(d.stop(),delete this.statWatchers[a]))};a.prototype.createReadStream=function(a,b){return new this.ReadStream(a,b)};a.prototype.createWriteStream=function(a,b){return new this.WriteStream(a,b)};a.prototype.watch=
function(a,b,d){a=m(a);var e=b;"function"===typeof b&&(d=b,e=null);var f=X(e);b=f.persistent;e=f.recursive;f=f.encoding;void 0===b&&(b=!0);void 0===e&&(e=!1);var g=new this.FSWatcher;g.start(a,b,e,f);d&&g.addListener("change",d);return g};a.fd=2147483647;return a}();b.Volume=a;var Ba=function(a){function b(b){var d=a.call(this)||this;d.onInterval=function(){try{var a=d.vol.statSync(d.filename);d.hasChanged(a)&&(d.emit("change",a,d.prev),d.prev=a)}finally{d.loop()}};d.vol=b;return d}J(b,a);b.prototype.loop=
function(){this.timeoutRef=this.setTimeout(this.onInterval,this.interval)};b.prototype.hasChanged=function(a){return a.mtimeMs>this.prev.mtimeMs||a.nlink!==this.prev.nlink?!0:!1};b.prototype.start=function(a,b,d){void 0===b&&(b=!0);void 0===d&&(d=5007);this.filename=m(a);this.setTimeout=b?setTimeout:Qc.default;this.interval=d;this.prev=this.vol.statSync(this.filename);this.loop()};b.prototype.stop=function(){clearTimeout(this.timeoutRef);R.default.nextTick(M,this)};return b}(t.EventEmitter);b.StatWatcher=
Ba;var W;La.inherits(A,V.Readable);b.ReadStream=A;A.prototype.open=function(){var a=this;this._vol.open(this.path,this.flags,this.mode,function(b,d){b?(a.autoClose&&a.destroy&&a.destroy(),a.emit("error",b)):(a.fd=d,a.emit("open",d),a.read())})};A.prototype._read=function(a){if("number"!==typeof this.fd)return this.once("open",function(){this._read(a)});if(!this.destroyed){if(!W||128>W.length-W.used)W=C.bufferAllocUnsafe(this._readableState.highWaterMark),W.used=0;var b=W,d=Math.min(W.length-W.used,
a),e=W.used;void 0!==this.pos&&(d=Math.min(this.end-this.pos+1,d));if(0>=d)return this.push(null);var f=this;this._vol.read(this.fd,W,W.used,d,this.pos,function(a,d){a?(f.autoClose&&f.destroy&&f.destroy(),f.emit("error",a)):(a=null,0<d&&(f.bytesRead+=d,a=b.slice(e,e+d)),f.push(a))});void 0!==this.pos&&(this.pos+=d);W.used+=d}};A.prototype._destroy=function(a,b){this.close(function(d){b(a||d)})};A.prototype.close=function(a){var b=this;if(a)this.once("close",a);if(this.closed||"number"!==typeof this.fd){if("number"!==
typeof this.fd){this.once("open",S);return}return R.default.nextTick(function(){return b.emit("close")})}this.closed=!0;this._vol.close(this.fd,function(a){a?b.emit("error",a):b.emit("close")});this.fd=null};La.inherits(B,V.Writable);b.WriteStream=B;B.prototype.open=function(){this._vol.open(this.path,this.flags,this.mode,function(a,b){a?(this.autoClose&&this.destroy&&this.destroy(),this.emit("error",a)):(this.fd=b,this.emit("open",b))}.bind(this))};B.prototype._write=function(a,b,d){if(!(a instanceof
C.Buffer))return this.emit("error",Error("Invalid data"));if("number"!==typeof this.fd)return this.once("open",function(){this._write(a,b,d)});var e=this;this._vol.write(this.fd,a,0,a.length,this.pos,function(a,b){if(a)return e.autoClose&&e.destroy&&e.destroy(),d(a);e.bytesWritten+=b;d()});void 0!==this.pos&&(this.pos+=a.length)};B.prototype._writev=function(a,b){if("number"!==typeof this.fd)return this.once("open",function(){this._writev(a,b)});for(var d=this,e=a.length,f=Array(e),g=0,h=0;h<e;h++){var k=
a[h].chunk;f[h]=k;g+=k.length}e=C.Buffer.concat(f);this._vol.write(this.fd,e,0,e.length,this.pos,function(a,e){if(a)return d.destroy&&d.destroy(),b(a);d.bytesWritten+=e;b()});void 0!==this.pos&&(this.pos+=g)};B.prototype._destroy=A.prototype._destroy;B.prototype.close=A.prototype.close;B.prototype.destroySoon=B.prototype.end;var Ca=function(a){function b(b){var d=a.call(this)||this;d._filename="";d._filenameEncoded="";d._recursive=!1;d._encoding=Y.ENCODING_UTF8;d._onNodeChange=function(){d._emit("change")};
d._onParentChild=function(a){a.getName()===d._getName()&&d._emit("rename")};d._emit=function(a){d.emit("change",a,d._filenameEncoded)};d._persist=function(){d._timer=setTimeout(d._persist,1E6)};d._vol=b;return d}J(b,a);b.prototype._getName=function(){return this._steps[this._steps.length-1]};b.prototype.start=function(a,b,d,e){void 0===b&&(b=!0);void 0===d&&(d=!1);void 0===e&&(e=Y.ENCODING_UTF8);this._filename=m(a);this._steps=p(this._filename);this._filenameEncoded=Y.strToEncoding(this._filename);
this._recursive=d;this._encoding=e;try{this._link=this._vol.getLinkOrThrow(this._filename,"FSWatcher")}catch(Bb){throw b=Error("watch "+this._filename+" "+Bb.code),b.code=Bb.code,b.errno=Bb.code,b;}this._link.getNode().on("change",this._onNodeChange);this._link.on("child:add",this._onNodeChange);this._link.on("child:delete",this._onNodeChange);if(a=this._link.parent)a.setMaxListeners(a.getMaxListeners()+1),a.on("child:delete",this._onParentChild);b&&this._persist()};b.prototype.close=function(){clearTimeout(this._timer);
this._link.getNode().removeListener("change",this._onNodeChange);var a=this._link.parent;a&&a.removeListener("child:delete",this._onParentChild)};return b}(t.EventEmitter);b.FSWatcher=Ca});D(na);var je=na.pathToFilename,ke=na.filenameToSteps,Uc=na.Volume,Cb=E(function(a,b){Object.defineProperty(b,"__esModule",{value:!0});b.fsProps="constants F_OK R_OK W_OK X_OK Stats".split(" ");b.fsSyncMethods="renameSync ftruncateSync truncateSync chownSync fchownSync lchownSync chmodSync fchmodSync lchmodSync statSync lstatSync fstatSync linkSync symlinkSync readlinkSync realpathSync unlinkSync rmdirSync mkdirSync mkdirpSync readdirSync closeSync openSync utimesSync futimesSync fsyncSync writeSync readSync readFileSync writeFileSync appendFileSync existsSync accessSync fdatasyncSync mkdtempSync copyFileSync createReadStream createWriteStream".split(" ");
b.fsAsyncMethods="rename ftruncate truncate chown fchown lchown chmod fchmod lchmod stat lstat fstat link symlink readlink realpath unlink rmdir mkdir mkdirp readdir close open utimes futimes fsync write read readFile writeFile appendFile exists access fdatasync mkdtemp copyFile watchFile unwatchFile watch".split(" ")});D(Cb);var Vc=E(function(a,b){function d(a){for(var b={F_OK:h,R_OK:k,W_OK:u,X_OK:m,constants:r.constants,Stats:Ia.default,Dirent:yb.default},d=0,e=f;d<e.length;d++){var l=e[d];"function"===
typeof a[l]&&(b[l]=a[l].bind(a))}d=0;for(e=g;d<e.length;d++)l=e[d],"function"===typeof a[l]&&(b[l]=a[l].bind(a));b.StatWatcher=a.StatWatcher;b.FSWatcher=a.FSWatcher;b.WriteStream=a.WriteStream;b.ReadStream=a.ReadStream;b.promises=a.promises;b._toUnixTimestamp=na.toUnixTimestamp;return b}var e=H&&H.__assign||function(){e=Object.assign||function(a){for(var b,d=1,e=arguments.length;d<e;d++){b=arguments[d];for(var f in b)Object.prototype.hasOwnProperty.call(b,f)&&(a[f]=b[f])}return a};return e.apply(this,
arguments)};Object.defineProperty(b,"__esModule",{value:!0});var f=Cb.fsSyncMethods,g=Cb.fsAsyncMethods,h=r.constants.F_OK,k=r.constants.R_OK,u=r.constants.W_OK,m=r.constants.X_OK;b.Volume=na.Volume;b.vol=new na.Volume;b.createFsFromVolume=d;b.fs=d(b.vol);a.exports=e(e({},a.exports),b.fs);a.exports.semantic=!0});D(Vc);var Wc=Vc.createFsFromVolume;ce.prototype.emit=function(a){for(var b,d,e=[],f=1;f<arguments.length;f++)e[f-1]=arguments[f];f=this.listeners(a);try{for(var g=Za(f),h=g.next();!h.done;h=
g.next()){var k=h.value;try{k.apply(void 0,ad(e))}catch(u){console.error(u)}}}catch(u){b={error:u}}finally{try{h&&!h.done&&(d=g.return)&&d.call(g)}finally{if(b)throw b.error;}}return 0<f.length};var Xc=function(){function a(){this.volume=new Uc;this.fs=Wc(this.volume);this.fromJSON({"/dev/stdin":"","/dev/stdout":"","/dev/stderr":""})}a.prototype._toJSON=function(a,d,e){void 0===d&&(d={});var b=!0,g;for(g in a.children){b=!1;var h=a.getChild(g);if(h){var k=h.getNode();k&&k.isFile()?(h=h.getPath(),
e&&(h=Va(e,h)),d[h]=k.getBuffer()):k&&k.isDirectory()&&this._toJSON(h,d,e)}}a=a.getPath();e&&(a=Va(e,a));a&&b&&(d[a]=null);return d};a.prototype.toJSON=function(a,d,e){var b,g;void 0===d&&(d={});void 0===e&&(e=!1);var h=[];if(a){a instanceof Array||(a=[a]);try{for(var k=Za(a),r=k.next();!r.done;r=k.next()){var m=je(r.value),p=this.volume.getResolvedLink(m);p&&h.push(p)}}catch(xb){var t={error:xb}}finally{try{r&&!r.done&&(b=k.return)&&b.call(k)}finally{if(t)throw t.error;}}}else h.push(this.volume.root);
if(!h.length)return d;try{for(var w=Za(h),v=w.next();!v.done;v=w.next())p=v.value,this._toJSON(p,d,e?p.getPath():"")}catch(xb){var x={error:xb}}finally{try{v&&!v.done&&(g=w.return)&&g.call(w)}finally{if(x)throw x.error;}}return d};a.prototype.fromJSONFixed=function(a,d){for(var b in d){var f=d[b];if(f?null!==Object.getPrototypeOf(f):null!==f){var g=ke(b);1<g.length&&(g="/"+g.slice(0,g.length-1).join("/"),a.mkdirpBase(g,511));a.writeFileSync(b,f||"")}else a.mkdirpBase(b,511)}};a.prototype.fromJSON=
function(a){this.volume=new Uc;this.fromJSONFixed(this.volume,a);this.fs=Wc(this.volume);this.volume.releasedFds=[0,1,2];a=this.volume.openSync("/dev/stderr","w");var b=this.volume.openSync("/dev/stdout","w"),e=this.volume.openSync("/dev/stdin","r");if(2!==a)throw Error("invalid handle for stderr: "+a);if(1!==b)throw Error("invalid handle for stdout: "+b);if(0!==e)throw Error("invalid handle for stdin: "+e);};a.prototype.getStdOut=function(){return Yc(this,void 0,void 0,function(){var a,d=this;return Zc(this,
function(b){a=new Promise(function(a){a(d.fs.readFileSync("/dev/stdout","utf8"))});return[2,a]})})};return a}();Ya.WasmFs=Xc;Ya.default=Xc;return Ya}({})
