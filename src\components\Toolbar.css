.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  min-height: 48px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.toolbar-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.toolbar-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.toolbar-btn.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  font-weight: 600;
}

.toolbar-btn.primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.toolbar-btn.running {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  cursor: not-allowed;
}

.toolbar-btn.running:hover {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  transform: none;
}

.toolbar-btn.icon-only {
  padding: 0.5rem;
  min-width: 36px;
  justify-content: center;
}

.toolbar-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-primary);
  margin: 0 0.25rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.status-dot.ready {
  background-color: var(--accent-color);
}

.status-dot.running {
  background-color: var(--warning-color);
  animation: pulse 1.5s ease-in-out infinite;
}

.status-dot.error {
  background-color: var(--danger-color);
}

.status-text {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

.stop-icon {
  animation: none;
}

.loading-dots {
  display: flex;
  gap: 2px;
  margin-left: 0.25rem;
}

.loading-dots span {
  width: 3px;
  height: 3px;
  background-color: currentColor;
  border-radius: 50%;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Button Ripple Effect */
.toolbar-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.toolbar-btn:active::before {
  width: 100px;
  height: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toolbar {
    padding: 0.375rem 0.75rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .toolbar-center {
    order: 3;
    flex-basis: 100%;
    justify-content: flex-start;
  }

  .toolbar-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .toolbar-btn span {
    display: none;
  }

  .toolbar-btn.primary span {
    display: inline;
  }

  .status-indicator {
    padding: 0.25rem 0.5rem;
  }

  .status-text {
    font-size: 0.6875rem;
  }
}

@media (max-width: 480px) {
  .toolbar-right {
    gap: 0.25rem;
  }

  .toolbar-divider {
    display: none;
  }

  .status-indicator {
    display: none;
  }
}
