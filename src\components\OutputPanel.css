.output-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-primary);
  position: relative;
}

.output-panel.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: var(--bg-primary);
}

.output-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  min-height: 44px;
}

.output-tabs {
  display: flex;
  gap: 0.25rem;
}

.output-tab {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  cursor: pointer;
}

.output-tab:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.output-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.output-actions {
  display: flex;
  gap: 0.25rem;
}

.output-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  border: 1px solid var(--border-primary);
  cursor: pointer;
}

.output-action-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-secondary);
}

.output-content {
  flex: 1;
  overflow: hidden;
}

/* Preview Container */
.preview-container {
  height: 100%;
  position: relative;
  background-color: white;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
}

.loading-icon {
  animation: spin 1s linear infinite;
  color: var(--primary-color);
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
  padding: 2rem;
  background-color: var(--bg-secondary);
}

.error-icon {
  color: var(--danger-color);
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-content h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.error-message {
  background-color: var(--bg-tertiary);
  color: var(--danger-color);
  padding: 1rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--danger-color);
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
  background-color: var(--bg-secondary);
}

.empty-icon {
  color: var(--text-muted);
  opacity: 0.5;
}

.empty-state h3 {
  color: var(--text-primary);
  font-size: 1.125rem;
  margin: 0;
}

.empty-state p {
  color: var(--text-muted);
  font-size: 0.875rem;
  max-width: 300px;
  line-height: 1.5;
  margin: 0;
}

/* Console Container */
.console-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

.console-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-tertiary);
}

.console-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.console-status {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-error {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--danger-color);
}

.status-success {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--accent-color);
}

.console-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  font-family: var(--font-family-mono);
  font-size: 0.75rem;
  line-height: 1.5;
}

.console-error,
.console-info {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  border-left: 3px solid;
}

.console-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-left-color: var(--danger-color);
}

.console-info {
  background-color: rgba(16, 185, 129, 0.1);
  border-left-color: var(--accent-color);
}

.console-timestamp {
  color: var(--text-muted);
  font-size: 0.6875rem;
  min-width: 80px;
}

.console-level {
  font-weight: 600;
  min-width: 50px;
}

.console-level.error {
  color: var(--danger-color);
}

.console-level.info {
  color: var(--accent-color);
}

.console-message {
  color: var(--text-primary);
  flex: 1;
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .output-header {
    padding: 0.375rem 0.75rem;
  }

  .output-tab {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }

  .output-tab span {
    display: none;
  }

  .console-content {
    padding: 0.75rem;
    font-size: 0.6875rem;
  }

  .console-timestamp {
    min-width: 60px;
    font-size: 0.625rem;
  }

  .console-level {
    min-width: 40px;
    font-size: 0.625rem;
  }
}
