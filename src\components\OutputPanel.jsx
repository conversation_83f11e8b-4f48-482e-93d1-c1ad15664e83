import { useState } from 'react'
import { Monitor, AlertCircle, CheckCircle, RefreshCw, Maximize2, Minimize2 } from 'lucide-react'
import InteractiveConsole from './InteractiveConsole'
import './OutputPanel.css'

const OutputPanel = ({
  output,
  error,
  isRunning,
  activeTab: currentTab,
  programState,
  onUserInput,
  onStopExecution,
  onExecute
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [activeTab, setActiveTab] = useState('preview')

  const tabs = [
    { id: 'preview', name: 'Preview', icon: Monitor },
    { id: 'console', name: 'Console', icon: AlertCircle }
  ]

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  return (
    <div className={`output-panel ${isFullscreen ? 'fullscreen' : ''}`}>
      <div className="output-header">
        <div className="output-tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                className={`output-tab ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                <Icon size={14} />
                <span>{tab.name}</span>
              </button>
            )
          })}
        </div>

        <div className="output-actions">
          <button
            className="output-action-btn"
            onClick={toggleFullscreen}
            title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </button>
        </div>
      </div>

      <div className="output-content">
        {activeTab === 'preview' && (
          <div className="preview-container">
            {isRunning ? (
              <div className="loading-state">
                <RefreshCw size={24} className="loading-icon" />
                <span>Executing code...</span>
              </div>
            ) : error ? (
              <div className="error-state">
                <AlertCircle size={24} className="error-icon" />
                <div className="error-content">
                  <h3>Execution Error</h3>
                  <pre className="error-message">{error}</pre>
                </div>
              </div>
            ) : output ? (
              output.type === 'c' ? (
                <div className="c-output-container">
                  <div className="c-output-header">
                    <h3>Program Output</h3>
                    {output.executionTime && (
                      <span className="execution-time">
                        Executed in {output.executionTime}ms
                      </span>
                    )}
                  </div>
                  <pre className="c-output-content">{output.content}</pre>
                </div>
              ) : (
                <iframe
                  className="preview-iframe"
                  srcDoc={output.content}
                  title="Code Output"
                  sandbox="allow-scripts allow-same-origin"
                />
              )
            ) : (
              <div className="empty-state">
                <Monitor size={48} className="empty-icon" />
                <h3>No Output</h3>
                <p>Click the "Run" button to execute your code and see the results here.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'console' && (
          <InteractiveConsole
            isRunning={isRunning}
            onExecute={onExecute}
            onStop={onStopExecution}
            programState={programState}
            onUserInput={onUserInput}
          />
        )}
      </div>
    </div>
  )
}

export default OutputPanel
