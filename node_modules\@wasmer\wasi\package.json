{"name": "@wasmer/wasi", "version": "1.2.2", "main": "dist/Library.cjs.min.js", "module": "dist/Library.esm.min.js", "unpkg": "dist/Library.umd.min.js", "types": "dist/lib.d.ts", "keywords": ["webassembly", "wasm", "wasi"], "description": "Isomorphic Javascript library for interacting with WASI Modules in Node.js and the Browser.", "author": "Wasmer Engineering Team <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wasmerio/wasmer-js/issues"}, "homepage": "https://github.com/wasmerio/wasmer-js", "repository": {"type": "git", "url": "https://github.com/wasmerio/wasmer-js"}, "publishConfig": {"access": "public"}, "scripts": {"build": "wasm-pack build --release --target web && wasm-opt pkg/wasmer_wasi_js_bg.wasm -O2 -o pkg/wasmer_wasi_js_bg.wasm && wasm-strip pkg/wasmer_wasi_js_bg.wasm && rollup -c --environment BUILD:production", "dev": "rollup -c -w", "lint": "", "test": "jest -c jest.config.js", "test:watch": "npm run test -- --watch", "test:coverage": "npm run test -- --coverage", "clean": "rimraf dist coverage", "prepare": "npm-run-all clean lint build test"}, "devDependencies": {"@rollup/plugin-node-resolve": "~15.0.1", "@rollup/plugin-terser": "~0.1.0", "@rollup/plugin-typescript": "^10.0.1", "@rollup/plugin-url": "^8.0.1", "@wasm-tool/wasm-pack-plugin": "1.6.0", "cross-env": "~7.0.3", "eslint": "~7.18.0", "jest": "^27.3.1", "npm-run-all": "~4.1.5", "rimraf": "~3.0.2", "rollup": "~3.5.1", "rollup-plugin-dts": "^5.0.0", "rollup-plugin-typescript2": "^0.34.1", "ts-loader": "^9.2.6", "tslib": "^2.3.1", "typescript": "^4.5.2"}, "browserslist": "> 0.5%, last 2 versions, Firefox ESR, not dead"}