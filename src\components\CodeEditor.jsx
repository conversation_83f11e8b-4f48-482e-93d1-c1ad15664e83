import { useRef, useEffect } from 'react'
import Editor from '@monaco-editor/react'
import './CodeEditor.css'

const CodeEditor = ({ language, value, onChange }) => {
  const editorRef = useRef(null)

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor

    // Configure Monaco Editor theme
    monaco.editor.defineTheme('tasocode-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'regexp', foreground: 'D16969' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'class', foreground: '4EC9B0' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'variable', foreground: '9CDCFE' },
        { token: 'constant', foreground: '4FC1FF' },
      ],
      colors: {
        'editor.background': '#1e293b',
        'editor.foreground': '#f8fafc',
        'editorLineNumber.foreground': '#64748b',
        'editorLineNumber.activeForeground': '#cbd5e1',
        'editor.selectionBackground': '#2563eb40',
        'editor.selectionHighlightBackground': '#2563eb20',
        'editorCursor.foreground': '#2563eb',
        'editor.findMatchBackground': '#f59e0b40',
        'editor.findMatchHighlightBackground': '#f59e0b20',
        'editorWidget.background': '#334155',
        'editorWidget.border': '#475569',
        'editorSuggestWidget.background': '#334155',
        'editorSuggestWidget.border': '#475569',
        'editorHoverWidget.background': '#334155',
        'editorHoverWidget.border': '#475569',
      }
    })

    monaco.editor.setTheme('tasocode-dark')

    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'JetBrains Mono, Fira Code, Consolas, monospace',
      lineHeight: 1.6,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      tabSize: 2,
      insertSpaces: true,
      detectIndentation: false,
      renderWhitespace: 'selection',
      renderLineHighlight: 'line',
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: true,
      smoothScrolling: true,
      mouseWheelZoom: true,
      folding: true,
      foldingHighlight: true,
      showFoldingControls: 'mouseover',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true
      }
    })

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // Save functionality will be handled by parent component
      console.log('Save shortcut pressed')
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      // Run code functionality will be handled by parent component
      console.log('Run shortcut pressed')
    })
  }

  const getLanguageMode = (lang) => {
    switch (lang) {
      case 'html':
        return 'html'
      case 'css':
        return 'css'
      case 'javascript':
        return 'javascript'
      default:
        return 'plaintext'
    }
  }

  return (
    <div className="code-editor">
      <Editor
        height="100%"
        language={getLanguageMode(language)}
        value={value}
        onChange={onChange}
        onMount={handleEditorDidMount}
        loading={
          <div className="editor-loading">
            <div className="loading-spinner"></div>
            <span>Loading Editor...</span>
          </div>
        }
        options={{
          theme: 'tasocode-dark'
        }}
      />
    </div>
  )
}

export default CodeEditor
