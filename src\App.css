.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-primary);
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* Allows flex child to shrink */
}

.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0.5rem 1rem;
}

.panel-group {
  flex: 1;
  background-color: var(--bg-primary);
}

.editor-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
}

.output-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
}

.panel-resize-handle {
  width: 4px;
  background-color: var(--border-primary);
  cursor: col-resize;
  transition: background-color var(--transition-fast);
  position: relative;
}

.panel-resize-handle:hover {
  background-color: var(--primary-color);
}

.panel-resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background-color: var(--text-muted);
  border-radius: 1px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.panel-resize-handle:hover::after {
  opacity: 1;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Slide In Animation */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Mobile Layout Styles */
.app.mobile-layout .panel-group {
  flex-direction: column;
}

.app.mobile-layout .editor-panel {
  border-right: none;
  border-bottom: 1px solid var(--border-primary);
}

.app.mobile-layout .panel-resize-handle {
  width: 100%;
  height: 6px;
  cursor: row-resize;
  background-color: var(--border-secondary);
  transition: all var(--transition-fast);
}

.app.mobile-layout .panel-resize-handle:hover {
  background-color: var(--primary-color);
  height: 8px;
}

.app.mobile-layout .panel-resize-handle::after {
  width: 30px;
  height: 3px;
  background-color: var(--text-muted);
}

.app.mobile-layout .panel-resize-handle:hover::after {
  background-color: white;
}

/* Mobile Panel Specific Styles */
.mobile-panels {
  gap: 0;
}

.desktop-panels {
  gap: 0;
}

.mobile-handle {
  background: linear-gradient(90deg, var(--border-primary), var(--border-secondary), var(--border-primary));
  position: relative;
}

.mobile-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 4px;
  background-color: var(--text-muted);
  border-radius: 2px;
  opacity: 0.7;
}

.desktop-handle {
  background: linear-gradient(180deg, var(--border-primary), var(--border-secondary), var(--border-primary));
}

/* Layout Transition Animation */
.app.transitioning {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app.transitioning .panel-group {
  transition: flex-direction 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app.transitioning .panel-resize-handle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app.transitioning .editor-panel,
.app.transitioning .output-panel {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Mobile Layout Body Class */
body.mobile-layout {
  --mobile-font-scale: 0.9;
  --mobile-spacing-scale: 0.8;
}

body.layout-transitioning {
  overflow: hidden;
}

body.layout-transitioning * {
  pointer-events: none;
}

/* Enhanced Mobile Optimizations */
.app.mobile-layout .editor-section {
  min-height: 0;
}

.app.mobile-layout .panel-group {
  min-height: 0;
  overflow: hidden;
}

/* Responsive Design Fallback */
@media (max-width: 768px) {
  .panel-group:not(.mobile-panels) {
    flex-direction: column;
  }

  .editor-panel:not(.app.mobile-layout .editor-panel) {
    border-right: none;
    border-bottom: 1px solid var(--border-primary);
  }

  .panel-resize-handle:not(.mobile-handle) {
    width: 100%;
    height: 4px;
    cursor: row-resize;
  }

  .panel-resize-handle:not(.mobile-handle)::after {
    width: 20px;
    height: 2px;
  }
}
