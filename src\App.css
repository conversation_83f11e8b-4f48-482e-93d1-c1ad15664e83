.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-primary);
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0.5rem 1rem;
}

.panel-group {
  flex: 1;
  background-color: var(--bg-primary);
}

.editor-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
}

.output-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
}

.panel-resize-handle {
  width: 4px;
  background-color: var(--border-primary);
  cursor: col-resize;
  transition: background-color var(--transition-fast);
  position: relative;
}

.panel-resize-handle:hover {
  background-color: var(--primary-color);
}

.panel-resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background-color: var(--text-muted);
  border-radius: 1px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.panel-resize-handle:hover::after {
  opacity: 1;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Slide In Animation */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .panel-group {
    flex-direction: column;
  }

  .editor-panel {
    border-right: none;
    border-bottom: 1px solid var(--border-primary);
  }

  .panel-resize-handle {
    width: 100%;
    height: 4px;
    cursor: row-resize;
  }

  .panel-resize-handle::after {
    width: 20px;
    height: 2px;
  }
}
