name: Continuous Integration

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  check:
    name: Compile and Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install Node
        uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: "1.64.0" # Required for [workspace.package]
          override: true
          target: wasm32-unknown-unknown
      - name: Rust Cache
        uses: Swatinem/rust-cache@v2
      - name: Install wasm-pack
        run: curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
      - name: Install wasm-strip and wasm-opt
        run: sudo apt-get update && sudo apt-get install -y wabt binaryen
      - name: Install Dependencies
        run: npm install
      - name: Build
        run: npm run build
      - name: Test
        run: npm run test
