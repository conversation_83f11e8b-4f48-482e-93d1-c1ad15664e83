import { Play, Square, Eye, <PERSON>Off, Settings, Download, Upload } from 'lucide-react'
import './Toolbar.css'

const Toolbar = ({ onRun, isRunning, onToggleOutput, isOutputVisible }) => {
  return (
    <div className="toolbar">
      <div className="toolbar-left">
        <button
          className={`toolbar-btn primary ${isRunning ? 'running' : ''}`}
          onClick={onRun}
          disabled={isRunning}
          title={isRunning ? 'Running...' : 'Run Code (Ctrl+Enter)'}
        >
          {isRunning ? (
            <>
              <Square size={16} className="stop-icon" />
              <span>Running...</span>
              <div className="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </>
          ) : (
            <>
              <Play size={16} />
              <span>Run</span>
            </>
          )}
        </button>

        <div className="toolbar-divider"></div>

        <button
          className="toolbar-btn"
          onClick={onToggleOutput}
          title={isOutputVisible ? 'Hide Output' : 'Show Output'}
        >
          {isOutputVisible ? (
            <>
              <EyeOff size={16} />
              <span>Hide Output</span>
            </>
          ) : (
            <>
              <Eye size={16} />
              <span>Show Output</span>
            </>
          )}
        </button>
      </div>

      <div className="toolbar-center">
        <div className="status-indicator">
          <div className={`status-dot ${isRunning ? 'running' : 'ready'}`}></div>
          <span className="status-text">
            {isRunning ? 'Executing...' : 'Ready'}
          </span>
        </div>
      </div>

      <div className="toolbar-right">
        <button
          className="toolbar-btn icon-only"
          title="Export Code"
        >
          <Download size={16} />
        </button>

        <button
          className="toolbar-btn icon-only"
          title="Import Code"
        >
          <Upload size={16} />
        </button>

        <div className="toolbar-divider"></div>

        <button
          className="toolbar-btn icon-only"
          title="Settings"
        >
          <Settings size={16} />
        </button>
      </div>
    </div>
  )
}

export default Toolbar
