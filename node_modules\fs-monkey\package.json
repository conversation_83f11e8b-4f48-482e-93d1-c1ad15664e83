{"name": "fs-monkey", "version": "0.3.3", "description": "Monkey patches for file system related things.", "main": "lib/index.js", "keywords": ["fs", "file", "file system", "monkey", "fsmo<PERSON>ey", "monkeyfs", "monkeypatch", "patch"], "repository": {"type": "git", "url": "https://github.com/streamich/fs-monkey.git"}, "scripts": {"build": "babel src --out-dir lib", "test": "jest", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "dependencies": {}, "devDependencies": {"jest": "21.2.1", "babel-jest": "21.2.0", "babel-core": "6.26.3", "babel-cli": "6.26.0", "babel-preset-es2015": "6.24.1", "source-map-support": "0.5.5", "semantic-release": "8.2.3", "@types/jest": "21.1.10", "@types/node": "8.10.14"}, "jest": {"collectCoverageFrom": ["src/**/*.js"], "transform": {"^.+\\.jsx?$": "babel-jest"}, "testRegex": ".*(__tests__/|/test/unit/).*(test|spec)\\.(t|j)sx?$"}}