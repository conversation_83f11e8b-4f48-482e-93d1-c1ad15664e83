[package]
name = "wasmer-wasi-js"
version = "1.2.2"
authors = ["Syr<PERSON> <<EMAIL>>"]
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
js-sys = "0.3.55"
wasm-bindgen = "0.2.73"
# Using the Wasmer 3.0.0 with the revert on stdin/err/out changes
# https://github.com/wasmerio/wasmer/pull/3344. Please use stable version as soon
# as wasmer is released
wasmer = { version = "3.0.2", default-features = false, features = ["js", "std"] }
wasmer-wasi = { version = "3.0.2", default-features = false, features = ["js"] }
wasmer-vfs = { version = "3.0.2", default-features = false, features = ["mem-fs"] }
wasm-bindgen-downcast = "0.1.1"

[profile.release]
lto = true
opt-level = 'z'

# TODO(<PERSON><PERSON><PERSON><PERSON><PERSON>): Remove this when Wasmer 3.1 comes out
# See https://github.com/wasmerio/wasmer-js/issues/312 for more.
[patch.crates-io]
wasmer = { git = "https://github.com/wasmerio/wasmer", default-features = false, features = ["js", "std"], rev = "ecde2aa" }
wasmer-wasi = { git = "https://github.com/wasmerio/wasmer", default-features = false, features = ["js"], rev = "ecde2aa" }
wasmer-vfs = { git = "https://github.com/wasmerio/wasmer", default-features = false, features = ["mem-fs"], rev = "ecde2aa" }
