import { useState, useEffect } from 'react'
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels'
import Header from './components/Header'
import TabBar from './components/TabBar'
import CodeEditor from './components/CodeEditor'
import OutputPanel from './components/OutputPanel'
import Toolbar from './components/Toolbar'
import { useCodeEditor } from './hooks/useCodeEditor'
import './App.css'

function App() {
  const {
    activeTab,
    setActiveTab,
    code,
    updateCode,
    runCode,
    output,
    isRunning,
    error,
    saveProject,
    loadProject,
    newProject
  } = useCodeEditor()

  const [isOutputVisible, setIsOutputVisible] = useState(true)

  return (
    <div className="app">
      <Header onSave={saveProject} onLoad={loadProject} onNew={newProject} />

      <div className="app-content">
        <div className="editor-section">
          <TabBar
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          <div className="editor-toolbar">
            <Toolbar
              onRun={runCode}
              isRunning={isRunning}
              onToggleOutput={() => setIsOutputVisible(!isOutputVisible)}
              isOutputVisible={isOutputVisible}
            />
          </div>

          <PanelGroup direction="horizontal" className="panel-group">
            <Panel defaultSize={50} minSize={30} className="editor-panel">
              <CodeEditor
                language={activeTab}
                value={code[activeTab]}
                onChange={(value) => updateCode(activeTab, value)}
              />
            </Panel>

            {isOutputVisible && (
              <>
                <PanelResizeHandle className="panel-resize-handle" />
                <Panel defaultSize={50} minSize={30} className="output-panel">
                  <OutputPanel
                    output={output}
                    error={error}
                    isRunning={isRunning}
                  />
                </Panel>
              </>
            )}
          </PanelGroup>
        </div>
      </div>
    </div>
  )
}

export default App
