{"hash": "60e33810", "configHash": "172b22c2", "lockfileHash": "b42e1b61", "browserHash": "b1d8b2ad", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "4e1bda68", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "7ee66d0b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "58ea98e5", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3ccc64c3", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "3029c467", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "5a57b1d7", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f95065b3", "needsInterop": true}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.js", "file": "react-resizable-panels.js", "fileHash": "87a23e9e", "needsInterop": false}}, "chunks": {"chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}