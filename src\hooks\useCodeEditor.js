import { useState, useCallback } from 'react'
import cCompiler from '../utils/cCompiler'

const DEFAULT_CODE = {
  html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TasoCode - Your Code</title>
    <style>
        /* Your CSS will be injected here */
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to TasoCode!</h1>
        <p>Start coding and see your results in real-time.</p>
        <button onclick="showMessage()">Click me!</button>
        <div id="output"></div>
    </div>
    
    <script>
        /* Your JavaScript will be injected here */
    </script>
</body>
</html>`,
  css: `/* TasoCode CSS */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 40px 20px;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

button {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    padding: 12px 24px;
    font-size: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

button:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
}

#output {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}`,
  javascript: `// TasoCode JavaScript
function showMessage() {
    const output = document.getElementById('output');
    const messages = [
        '🎉 Hello from TasoCode!',
        '✨ Your code is working perfectly!',
        '🚀 Keep coding and creating amazing things!',
        '💡 The possibilities are endless!',
        '🎨 Beautiful code, beautiful results!'
    ];
    
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    output.innerHTML = \`
        <div style="animation: fadeIn 0.5s ease-in;">
            <h3 style="margin: 0; color: #fff;">\${randomMessage}</h3>
            <p style="margin: 10px 0 0 0; opacity: 0.8;">
                Generated at: \${new Date().toLocaleTimeString()}
            </p>
        </div>
    \`;
}

// Add some interactive animations
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 TasoCode is ready!');
    
    // Add fade-in animation
    const style = document.createElement('style');
    style.textContent = \`
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    \`;
    document.head.appendChild(style);
});`,
  c: `#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

int main() {
    // Welcome to TasoCode C Programming!
    printf("Hello, World!\\n");
    printf("Welcome to TasoCode C Compiler\\n");

    // Demonstrate basic C features
    int number = 42;
    float pi = 3.14159;
    char message[] = "C Programming is awesome!";

    printf("\\n--- Basic Data Types ---\\n");
    printf("Integer: %d\\n", number);
    printf("Float: %.2f\\n", pi);
    printf("String: %s\\n", message);

    // Simple calculation
    printf("\\n--- Simple Calculation ---\\n");
    int a = 10, b = 20;
    printf("%d + %d = %d\\n", a, b, a + b);
    printf("%d * %d = %d\\n", a, b, a * b);
    printf("Square root of %d = %.2f\\n", number, sqrt(number));

    // Array demonstration
    printf("\\n--- Array Example ---\\n");
    int numbers[] = {1, 2, 3, 4, 5};
    int size = sizeof(numbers) / sizeof(numbers[0]);

    printf("Array elements: ");
    for(int i = 0; i < size; i++) {
        printf("%d ", numbers[i]);
    }
    printf("\\n");

    // String manipulation
    printf("\\n--- String Operations ---\\n");
    char greeting[50];
    strcpy(greeting, "Hello, ");
    strcat(greeting, "TasoCode!");
    printf("Concatenated string: %s\\n", greeting);
    printf("String length: %lu\\n", strlen(greeting));

    printf("\\n--- Program completed successfully! ---\\n");
    return 0;
}`
}

export const useCodeEditor = () => {
  const [activeTab, setActiveTab] = useState('html')
  const [code, setCode] = useState(() => {
    // Try to load from localStorage
    const savedCode = localStorage.getItem('tasocode-project')
    if (savedCode) {
      try {
        return JSON.parse(savedCode)
      } catch (e) {
        console.error('Failed to parse saved code:', e)
      }
    }
    return DEFAULT_CODE
  })
  const [output, setOutput] = useState('')
  const [error, setError] = useState('')
  const [isRunning, setIsRunning] = useState(false)

  const updateCode = useCallback((language, newCode) => {
    setCode(prev => {
      const updated = { ...prev, [language]: newCode }
      // Auto-save to localStorage
      localStorage.setItem('tasocode-project', JSON.stringify(updated))
      return updated
    })
  }, [])

  const runCode = useCallback(async (activeTab) => {
    setIsRunning(true)
    setError('')

    try {
      if (activeTab === 'c') {
        // Handle C code compilation and execution
        const result = await cCompiler.compile(code.c)

        if (result.success) {
          // Format C program output for display
          const formattedOutput = result.output.join('')
          setOutput({
            type: 'c',
            content: formattedOutput,
            executionTime: result.executionTime
          })
        } else {
          setError(result.errors.join('\n'))
        }
      } else {
        // Handle web languages (HTML, CSS, JavaScript)
        await new Promise(resolve => setTimeout(resolve, 500))

        // Combine HTML, CSS, and JavaScript
        let htmlContent = code.html

        // Inject CSS
        if (code.css.trim()) {
          const cssInjection = `<style>${code.css}</style>`
          htmlContent = htmlContent.replace('/* Your CSS will be injected here */', code.css)

          // If no CSS placeholder found, inject in head
          if (!htmlContent.includes(code.css)) {
            htmlContent = htmlContent.replace('</head>', `${cssInjection}\n</head>`)
          }
        }

        // Inject JavaScript
        if (code.javascript.trim()) {
          const jsInjection = `<script>${code.javascript}</script>`
          htmlContent = htmlContent.replace('/* Your JavaScript will be injected here */', code.javascript)

          // If no JS placeholder found, inject before closing body
          if (!htmlContent.includes(code.javascript)) {
            htmlContent = htmlContent.replace('</body>', `${jsInjection}\n</body>`)
          }
        }

        setOutput({
          type: 'web',
          content: htmlContent
        })
      }
    } catch (err) {
      setError(err.message || 'An error occurred while executing the code')
    } finally {
      setIsRunning(false)
    }
  }, [code])

  const saveProject = useCallback(() => {
    const projectData = {
      code,
      timestamp: new Date().toISOString(),
      name: `TasoCode Project - ${new Date().toLocaleDateString()}`
    }
    
    const dataStr = JSON.stringify(projectData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `tasocode-project-${Date.now()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [code])

  const loadProject = useCallback(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    
    input.onchange = (e) => {
      const file = e.target.files[0]
      if (!file) return
      
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const projectData = JSON.parse(e.target.result)
          if (projectData.code) {
            setCode(projectData.code)
            localStorage.setItem('tasocode-project', JSON.stringify(projectData.code))
          }
        } catch (err) {
          setError('Failed to load project file')
        }
      }
      reader.readAsText(file)
    }
    
    input.click()
  }, [])

  const newProject = useCallback(() => {
    if (confirm('Are you sure you want to create a new project? Unsaved changes will be lost.')) {
      setCode(DEFAULT_CODE)
      setOutput('')
      setError('')
      setActiveTab('html')
      localStorage.removeItem('tasocode-project')
    }
  }, [])

  return {
    activeTab,
    setActiveTab,
    code,
    updateCode,
    runCode,
    output,
    isRunning,
    error,
    saveProject,
    loadProject,
    newProject
  }
}
