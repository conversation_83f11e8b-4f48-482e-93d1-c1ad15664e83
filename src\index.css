:root {
  /* Color Palette */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;

  /* Background Colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-hover: #334155;

  /* Text Colors */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;

  /* Border Colors */
  --border-primary: #334155;
  --border-secondary: #475569;
  --border-focus: #2563eb;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-family-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.25s ease-in-out;
  --transition-slow: 0.35s ease-in-out;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

#root {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-secondary);
}

/* Selection Styling */
::selection {
  background-color: var(--primary-color);
  color: white;
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Button Reset */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* Input Reset */
input, textarea {
  border: none;
  background: none;
  font-family: inherit;
  color: inherit;
}

/* Link Reset */
a {
  color: inherit;
  text-decoration: none;
}

/* Mobile Layout Global Styles */
body.mobile-layout {
  /* Enhanced touch targets */
  --touch-target-size: 44px;

  /* Mobile-optimized spacing */
  --mobile-padding: 1rem;
  --mobile-margin: 0.75rem;

  /* Mobile-friendly font sizes */
  --mobile-font-base: 0.9rem;
  --mobile-font-small: 0.8rem;
  --mobile-font-large: 1.1rem;
}

body.mobile-layout * {
  /* Improve touch scrolling */
  -webkit-overflow-scrolling: touch;

  /* Prevent text selection on UI elements */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection in content areas */
body.mobile-layout .monaco-editor,
body.mobile-layout .console-input,
body.mobile-layout .console-content,
body.mobile-layout input,
body.mobile-layout textarea {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Mobile-specific animations */
body.mobile-layout .fade-in {
  animation: mobileSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes mobileSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Layout transition styles */
body.layout-transitioning {
  overflow: hidden;
}

body.layout-transitioning * {
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
