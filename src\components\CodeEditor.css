.code-editor {
  height: 100%;
  width: 100%;
  position: relative;
  background-color: var(--bg-secondary);
}

.editor-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Monaco Editor Customizations */
.monaco-editor {
  background-color: var(--bg-secondary) !important;
}

.monaco-editor .margin {
  background-color: var(--bg-secondary) !important;
}

.monaco-editor .monaco-editor-background {
  background-color: var(--bg-secondary) !important;
}

.monaco-editor .current-line {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: none !important;
}

.monaco-editor .selected-text {
  background-color: var(--primary-color) !important;
  opacity: 0.3;
}

.monaco-editor .cursor {
  background-color: var(--primary-color) !important;
}

/* Scrollbar Styling for Monaco */
.monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
  background-color: var(--bg-tertiary) !important;
  border-radius: 4px !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider:hover {
  background-color: var(--border-secondary) !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar {
  background-color: transparent !important;
}

/* Line Numbers */
.monaco-editor .line-numbers {
  color: var(--text-muted) !important;
}

.monaco-editor .current-line ~ .line-numbers {
  color: var(--text-secondary) !important;
  font-weight: 500;
}

/* Minimap (if enabled) */
.monaco-editor .minimap {
  background-color: var(--bg-tertiary) !important;
}

.monaco-editor .minimap-slider {
  background-color: rgba(37, 99, 235, 0.2) !important;
}

.monaco-editor .minimap-slider:hover {
  background-color: rgba(37, 99, 235, 0.3) !important;
}

/* Suggestions Widget */
.monaco-editor .suggest-widget {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-lg) !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row {
  background-color: transparent !important;
  color: var(--text-primary) !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row:hover {
  background-color: var(--bg-hover) !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.focused {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Hover Widget */
.monaco-editor .monaco-hover {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-lg) !important;
}

.monaco-editor .monaco-hover .hover-contents {
  color: var(--text-primary) !important;
}

/* Find Widget */
.monaco-editor .find-widget {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-md) !important;
}

.monaco-editor .find-widget input {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-sm) !important;
}

.monaco-editor .find-widget input:focus {
  border-color: var(--border-focus) !important;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2) !important;
}

/* Context Menu */
.monaco-menu {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-lg) !important;
}

.monaco-menu .monaco-action-bar .action-item .action-label {
  color: var(--text-primary) !important;
}

.monaco-menu .monaco-action-bar .action-item .action-label:hover {
  background-color: var(--bg-hover) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .monaco-editor {
    font-size: 12px !important;
  }
  
  .editor-loading {
    font-size: 0.75rem;
  }
  
  .loading-spinner {
    width: 24px;
    height: 24px;
    border-width: 2px;
  }
}
