// C Compiler utility using WebAssembly
// For now, we'll use a simulated compiler that can handle basic C programs
// In a production environment, you would integrate with a real WASM C compiler

class CCompiler {
  constructor() {
    this.isInitialized = false
    this.output = []
    this.errors = []
  }

  async initialize() {
    if (this.isInitialized) return
    
    // Simulate compiler initialization
    await new Promise(resolve => setTimeout(resolve, 100))
    this.isInitialized = true
  }

  // Simulate C code compilation and execution
  async compile(sourceCode) {
    await this.initialize()
    
    this.output = []
    this.errors = []

    try {
      // Basic syntax validation
      this.validateSyntax(sourceCode)
      
      // Simulate compilation process
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // Execute the code (simulate)
      const result = await this.executeCode(sourceCode)
      
      return {
        success: true,
        output: result.output,
        errors: result.errors,
        executionTime: result.executionTime
      }
    } catch (error) {
      return {
        success: false,
        output: [],
        errors: [error.message],
        executionTime: 0
      }
    }
  }

  validateSyntax(code) {
    // Basic syntax validation
    const lines = code.split('\n')
    let braceCount = 0
    let parenCount = 0
    let hasMain = false
    let hasInclude = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // Check for main function
      if (line.includes('int main') || line.includes('void main')) {
        hasMain = true
      }
      
      // Check for includes
      if (line.startsWith('#include')) {
        hasInclude = true
      }
      
      // Count braces and parentheses
      for (const char of line) {
        if (char === '{') braceCount++
        if (char === '}') braceCount--
        if (char === '(') parenCount++
        if (char === ')') parenCount--
      }
      
      // Check for common syntax errors
      if (line.endsWith(';') && line.includes('if') && !line.includes(')')) {
        throw new Error(`Syntax error at line ${i + 1}: Missing closing parenthesis in if statement`)
      }
    }

    if (!hasMain) {
      throw new Error('Compilation error: No main function found')
    }

    if (braceCount !== 0) {
      throw new Error('Compilation error: Mismatched braces')
    }

    if (parenCount !== 0) {
      throw new Error('Compilation error: Mismatched parentheses')
    }
  }

  async executeCode(sourceCode) {
    const startTime = Date.now()
    const output = []
    const errors = []

    try {
      // Enhanced printf parsing with better variable simulation
      const lines = sourceCode.split('\n')

      // Extract variable declarations and values
      const variables = this.extractVariables(sourceCode)

      for (const line of lines) {
        const trimmedLine = line.trim()

        if (trimmedLine.includes('printf')) {
          const printfOutput = this.simulatePrintfLine(trimmedLine, variables, sourceCode)
          if (printfOutput) {
            output.push(printfOutput)
          }
        }
      }

      // If no printf found, add default output
      if (output.length === 0) {
        output.push('Program executed successfully (no output)')
      }

    } catch (error) {
      errors.push(`Runtime error: ${error.message}`)
    }

    const executionTime = Date.now() - startTime

    return {
      output,
      errors,
      executionTime
    }
  }

  extractVariables(sourceCode) {
    const variables = {}
    const lines = sourceCode.split('\n')

    for (const line of lines) {
      const trimmedLine = line.trim()

      // Extract integer declarations
      const intMatch = trimmedLine.match(/int\s+(\w+)\s*=\s*(\d+)/)
      if (intMatch) {
        variables[intMatch[1]] = parseInt(intMatch[2])
      }

      // Extract float declarations
      const floatMatch = trimmedLine.match(/float\s+(\w+)\s*=\s*([\d.]+)/)
      if (floatMatch) {
        variables[floatMatch[1]] = parseFloat(floatMatch[2])
      }

      // Extract char array declarations
      const charArrayMatch = trimmedLine.match(/char\s+(\w+)\[\]\s*=\s*"([^"]*)"/)
      if (charArrayMatch) {
        variables[charArrayMatch[1]] = charArrayMatch[2]
      }

      // Extract simple calculations
      const calcMatch = trimmedLine.match(/int\s+(\w+)\s*=\s*(\d+)\s*,\s*(\w+)\s*=\s*(\d+)/)
      if (calcMatch) {
        variables[calcMatch[1]] = parseInt(calcMatch[2])
        variables[calcMatch[3]] = parseInt(calcMatch[4])
      }
    }

    return variables
  }

  simulatePrintfLine(line, variables, sourceCode) {
    // Extract printf format string and arguments
    const printfMatch = line.match(/printf\s*\(\s*"([^"]*)"([^)]*)\)/)
    if (!printfMatch) return null

    let formatString = printfMatch[1]
    const args = printfMatch[2]

    // Handle escape sequences
    formatString = formatString
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\"/g, '"')
      .replace(/\\\\/g, '\\')

    // Handle format specifiers with actual values
    if (args && args.trim()) {
      const argList = args.split(',').map(arg => arg.trim().replace(/^\s*,\s*/, ''))
      let argIndex = 0

      formatString = formatString.replace(/%[diouxX]/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]

          // Handle calculations
          if (arg.includes('+')) {
            const parts = arg.split('+').map(p => p.trim())
            const val1 = variables[parts[0]] || parseInt(parts[0]) || 0
            const val2 = variables[parts[1]] || parseInt(parts[1]) || 0
            return val1 + val2
          }
          if (arg.includes('*')) {
            const parts = arg.split('*').map(p => p.trim())
            const val1 = variables[parts[0]] || parseInt(parts[0]) || 0
            const val2 = variables[parts[1]] || parseInt(parts[1]) || 0
            return val1 * val2
          }

          return variables[arg] || parseInt(arg) || 42
        }
        return 42
      })

      formatString = formatString.replace(/%.?\d*f/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]

          // Handle sqrt function
          if (arg.includes('sqrt')) {
            const numberMatch = arg.match(/sqrt\((\w+|\d+)\)/)
            if (numberMatch) {
              const value = variables[numberMatch[1]] || parseInt(numberMatch[1]) || 42
              return Math.sqrt(value).toFixed(2)
            }
          }

          return variables[arg] || parseFloat(arg) || 3.14
        }
        return '3.14'
      })

      formatString = formatString.replace(/%s/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]
          return variables[arg] || 'string'
        }
        return 'string'
      })

      formatString = formatString.replace(/%lu/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]
          if (arg.includes('strlen')) {
            const strMatch = arg.match(/strlen\((\w+)\)/)
            if (strMatch && variables[strMatch[1]]) {
              return variables[strMatch[1]].length
            }
          }
          return variables[arg] || 10
        }
        return 10
      })
    } else {
      // No arguments, use default values
      formatString = formatString
        .replace(/%[diouxX]/g, '42')
        .replace(/%.?\d*f/g, '3.14')
        .replace(/%s/g, 'string')
        .replace(/%lu/g, '10')
    }

    return formatString
  }


}

// Export singleton instance
export const cCompiler = new CCompiler()
export default cCompiler
