// C Compiler utility using WebAssembly
// For now, we'll use a simulated compiler that can handle basic C programs
// In a production environment, you would integrate with a real WASM C compiler

class CCompiler {
  constructor() {
    this.isInitialized = false
    this.output = []
    this.errors = []
    this.programState = null
    this.inputCallback = null
    this.executionSteps = []
    this.currentStep = 0
    this.variables = {}
    this.isInteractive = false
  }

  async initialize() {
    if (this.isInitialized) return
    
    // Simulate compiler initialization
    await new Promise(resolve => setTimeout(resolve, 100))
    this.isInitialized = true
  }

  // Simulate C code compilation and execution
  async compile(sourceCode, onStateChange = null) {
    await this.initialize()

    this.output = []
    this.errors = []
    this.programState = {
      isRunning: false,
      waitingForInput: false,
      output: [],
      error: null,
      executionTime: 0
    }
    this.onStateChange = onStateChange

    try {
      // Basic syntax validation
      this.validateSyntax(sourceCode)

      // Check if program has interactive input
      this.isInteractive = this.hasInputFunctions(sourceCode)

      // Simulate compilation process
      await new Promise(resolve => setTimeout(resolve, 500))

      if (this.isInteractive) {
        // Prepare for interactive execution
        this.prepareInteractiveExecution(sourceCode)
        return {
          success: true,
          isInteractive: true,
          programState: this.programState
        }
      } else {
        // Execute non-interactive code
        const result = await this.executeCode(sourceCode)
        return {
          success: true,
          isInteractive: false,
          output: result.output,
          errors: result.errors,
          executionTime: result.executionTime
        }
      }
    } catch (error) {
      return {
        success: false,
        isInteractive: false,
        output: [],
        errors: [error.message],
        executionTime: 0
      }
    }
  }

  // Start interactive execution
  async startInteractiveExecution() {
    if (!this.isInteractive || !this.executionSteps.length) return

    this.programState.isRunning = true
    this.currentStep = 0
    this.notifyStateChange()

    await this.executeNextStep()
  }

  // Handle user input during interactive execution
  async handleUserInput(input) {
    if (!this.programState.waitingForInput) return

    // Store the input value
    const currentStep = this.executionSteps[this.currentStep]
    if (currentStep && currentStep.type === 'input') {
      currentStep.inputValue = input
      this.variables[currentStep.variable] = this.parseInputValue(input, currentStep.dataType)
    }

    this.programState.waitingForInput = false
    this.notifyStateChange()

    // Continue execution
    this.currentStep++
    await this.executeNextStep()
  }

  // Execute the next step in interactive mode
  async executeNextStep() {
    if (this.currentStep >= this.executionSteps.length) {
      // Execution complete
      this.programState.isRunning = false
      this.programState.executionTime = Date.now() - this.startTime
      this.notifyStateChange()
      return
    }

    const step = this.executionSteps[this.currentStep]

    switch (step.type) {
      case 'output':
        // Process the output with current variable values
        let outputContent = step.content
        if (step.originalLine) {
          // Re-process printf with current variables
          outputContent = this.simulatePrintfLine(step.originalLine, this.variables, '')
        }
        this.programState.output.push(outputContent)
        this.notifyStateChange()
        this.currentStep++
        // Small delay for realistic execution
        setTimeout(() => this.executeNextStep(), 100)
        break

      case 'input':
        this.programState.waitingForInput = true
        this.programState.inputPrompt = step.prompt
        this.notifyStateChange()
        // Wait for user input (handled by handleUserInput)
        break

      default:
        this.currentStep++
        setTimeout(() => this.executeNextStep(), 50)
        break
    }
  }

  validateSyntax(code) {
    // Basic syntax validation
    const lines = code.split('\n')
    let braceCount = 0
    let parenCount = 0
    let hasMain = false
    let hasInclude = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // Check for main function
      if (line.includes('int main') || line.includes('void main')) {
        hasMain = true
      }
      
      // Check for includes
      if (line.startsWith('#include')) {
        hasInclude = true
      }
      
      // Count braces and parentheses
      for (const char of line) {
        if (char === '{') braceCount++
        if (char === '}') braceCount--
        if (char === '(') parenCount++
        if (char === ')') parenCount--
      }
      
      // Check for common syntax errors
      if (line.endsWith(';') && line.includes('if') && !line.includes(')')) {
        throw new Error(`Syntax error at line ${i + 1}: Missing closing parenthesis in if statement`)
      }
    }

    if (!hasMain) {
      throw new Error('Compilation error: No main function found')
    }

    if (braceCount !== 0) {
      throw new Error('Compilation error: Mismatched braces')
    }

    if (parenCount !== 0) {
      throw new Error('Compilation error: Mismatched parentheses')
    }
  }

  async executeCode(sourceCode) {
    const startTime = Date.now()
    const output = []
    const errors = []

    try {
      // Enhanced printf parsing with better variable simulation
      const lines = sourceCode.split('\n')

      // Extract variable declarations and values
      const variables = this.extractVariables(sourceCode)

      for (const line of lines) {
        const trimmedLine = line.trim()

        if (trimmedLine.includes('printf')) {
          const printfOutput = this.simulatePrintfLine(trimmedLine, variables, sourceCode)
          if (printfOutput) {
            output.push(printfOutput)
          }
        }
      }

      // If no printf found, add default output
      if (output.length === 0) {
        output.push('Program executed successfully (no output)')
      }

    } catch (error) {
      errors.push(`Runtime error: ${error.message}`)
    }

    const executionTime = Date.now() - startTime

    return {
      output,
      errors,
      executionTime
    }
  }

  extractVariables(sourceCode) {
    const variables = {}
    const lines = sourceCode.split('\n')

    for (const line of lines) {
      const trimmedLine = line.trim()

      // Extract integer declarations
      const intMatch = trimmedLine.match(/int\s+(\w+)\s*=\s*(\d+)/)
      if (intMatch) {
        variables[intMatch[1]] = parseInt(intMatch[2])
      }

      // Extract float declarations
      const floatMatch = trimmedLine.match(/float\s+(\w+)\s*=\s*([\d.]+)/)
      if (floatMatch) {
        variables[floatMatch[1]] = parseFloat(floatMatch[2])
      }

      // Extract char array declarations
      const charArrayMatch = trimmedLine.match(/char\s+(\w+)\[\]\s*=\s*"([^"]*)"/)
      if (charArrayMatch) {
        variables[charArrayMatch[1]] = charArrayMatch[2]
      }

      // Extract simple calculations
      const calcMatch = trimmedLine.match(/int\s+(\w+)\s*=\s*(\d+)\s*,\s*(\w+)\s*=\s*(\d+)/)
      if (calcMatch) {
        variables[calcMatch[1]] = parseInt(calcMatch[2])
        variables[calcMatch[3]] = parseInt(calcMatch[4])
      }
    }

    return variables
  }

  simulatePrintfLine(line, variables, sourceCode) {
    // Extract printf format string and arguments
    const printfMatch = line.match(/printf\s*\(\s*"([^"]*)"([^)]*)\)/)
    if (!printfMatch) return null

    let formatString = printfMatch[1]
    const args = printfMatch[2]

    // Handle escape sequences
    formatString = formatString
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\"/g, '"')
      .replace(/\\\\/g, '\\')

    // Handle format specifiers with actual values
    if (args && args.trim()) {
      const argList = args.split(',').map(arg => arg.trim().replace(/^\s*,\s*/, ''))
      let argIndex = 0

      formatString = formatString.replace(/%[diouxX]/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]

          // Handle calculations
          if (arg.includes('+')) {
            const parts = arg.split('+').map(p => p.trim())
            const val1 = variables[parts[0]] || parseInt(parts[0]) || 0
            const val2 = variables[parts[1]] || parseInt(parts[1]) || 0
            return val1 + val2
          }
          if (arg.includes('*')) {
            const parts = arg.split('*').map(p => p.trim())
            const val1 = variables[parts[0]] || parseInt(parts[0]) || 0
            const val2 = variables[parts[1]] || parseInt(parts[1]) || 0
            return val1 * val2
          }

          return variables[arg] || parseInt(arg) || 42
        }
        return 42
      })

      formatString = formatString.replace(/%.?\d*f/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]

          // Handle sqrt function
          if (arg.includes('sqrt')) {
            const numberMatch = arg.match(/sqrt\((\w+|\d+)\)/)
            if (numberMatch) {
              const value = variables[numberMatch[1]] || parseInt(numberMatch[1]) || 42
              return Math.sqrt(value).toFixed(2)
            }
          }

          return variables[arg] || parseFloat(arg) || 3.14
        }
        return '3.14'
      })

      formatString = formatString.replace(/%s/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]
          return variables[arg] || 'string'
        }
        return 'string'
      })

      formatString = formatString.replace(/%lu/g, () => {
        if (argIndex < argList.length) {
          const arg = argList[argIndex++]
          if (arg.includes('strlen')) {
            const strMatch = arg.match(/strlen\((\w+)\)/)
            if (strMatch && variables[strMatch[1]]) {
              return variables[strMatch[1]].length
            }
          }
          return variables[arg] || 10
        }
        return 10
      })
    } else {
      // No arguments, use default values
      formatString = formatString
        .replace(/%[diouxX]/g, '42')
        .replace(/%.?\d*f/g, '3.14')
        .replace(/%s/g, 'string')
        .replace(/%lu/g, '10')
    }

    return formatString
  }

  // Check if source code contains input functions
  hasInputFunctions(sourceCode) {
    const inputFunctions = ['scanf', 'getchar', 'gets', 'fgets', 'getline']
    return inputFunctions.some(func => sourceCode.includes(func))
  }

  // Prepare execution steps for interactive mode
  prepareInteractiveExecution(sourceCode) {
    this.executionSteps = []
    this.variables = this.extractVariables(sourceCode)
    this.startTime = Date.now()

    const lines = sourceCode.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      if (line.includes('printf')) {
        // Add output step (but don't process variables yet for interactive mode)
        const printfMatch = line.match(/printf\s*\(\s*"([^"]*)"/)
        if (printfMatch) {
          let formatString = printfMatch[1]
          formatString = formatString
            .replace(/\\n/g, '\n')
            .replace(/\\t/g, '\t')
            .replace(/\\"/g, '"')
            .replace(/\\\\/g, '\\')

          this.executionSteps.push({
            type: 'output',
            content: formatString,
            lineNumber: i + 1,
            originalLine: line
          })
        }
      } else if (line.includes('scanf')) {
        // Add input step
        const inputStep = this.parseScanfStatement(line)
        if (inputStep) {
          this.executionSteps.push({
            type: 'input',
            ...inputStep,
            lineNumber: i + 1
          })
        }
      } else if (line.includes('getchar')) {
        // Add character input step
        this.executionSteps.push({
          type: 'input',
          variable: 'char_input',
          dataType: 'char',
          prompt: 'Enter a character:',
          lineNumber: i + 1
        })
      }
    }
  }

  // Parse scanf statement to extract input requirements
  parseScanfStatement(line) {
    const scanfMatch = line.match(/scanf\s*\(\s*"([^"]*)"[^)]*&(\w+)[^)]*\)/)
    if (!scanfMatch) return null

    const formatString = scanfMatch[1]
    const variable = scanfMatch[2]

    let dataType = 'string'
    let prompt = 'Enter value:'

    if (formatString.includes('%d') || formatString.includes('%i')) {
      dataType = 'int'
      prompt = 'Enter an integer:'
    } else if (formatString.includes('%f') || formatString.includes('%lf')) {
      dataType = 'float'
      prompt = 'Enter a number:'
    } else if (formatString.includes('%c')) {
      dataType = 'char'
      prompt = 'Enter a character:'
    } else if (formatString.includes('%s')) {
      dataType = 'string'
      prompt = 'Enter text:'
    }

    return {
      variable,
      dataType,
      prompt,
      formatString
    }
  }

  // Parse input value based on data type
  parseInputValue(input, dataType) {
    switch (dataType) {
      case 'int':
        return parseInt(input) || 0
      case 'float':
        return parseFloat(input) || 0.0
      case 'char':
        return input.charAt(0) || ''
      case 'string':
      default:
        return input || ''
    }
  }

  // Notify state change to UI
  notifyStateChange() {
    if (this.onStateChange) {
      this.onStateChange({ ...this.programState })
    }
  }

  // Stop interactive execution
  stopExecution() {
    this.programState.isRunning = false
    this.programState.waitingForInput = false
    this.notifyStateChange()
  }
}

// Export singleton instance
export const cCompiler = new CCompiler()
export default cCompiler
