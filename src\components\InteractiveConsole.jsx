import { useState, useEffect, useRef } from 'react'
import { Terminal, Play, Square } from 'lucide-react'
import './InteractiveConsole.css'

const InteractiveConsole = ({ 
  isRunning, 
  onExecute, 
  onStop, 
  programState,
  onUserInput 
}) => {
  const [inputValue, setInputValue] = useState('')
  const [consoleHistory, setConsoleHistory] = useState([])
  const inputRef = useRef(null)
  const consoleRef = useRef(null)

  // Auto-scroll to bottom when new content is added
  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight
    }
  }, [consoleHistory, programState])

  // Focus input when waiting for user input
  useEffect(() => {
    if (programState?.waitingForInput && inputRef.current) {
      inputRef.current.focus()
    }
  }, [programState?.waitingForInput])

  // Update console history when program state changes
  useEffect(() => {
    if (programState?.output && programState.output.length > 0) {
      setConsoleHistory(prev => {
        const newHistory = [...prev]
        
        // Add new output entries
        programState.output.forEach((entry, index) => {
          const existingIndex = newHistory.findIndex(h => h.id === `output-${index}`)
          if (existingIndex === -1) {
            newHistory.push({
              id: `output-${index}`,
              type: 'output',
              content: entry,
              timestamp: new Date().toLocaleTimeString()
            })
          }
        })
        
        return newHistory
      })
    }

    // Add error messages
    if (programState?.error) {
      setConsoleHistory(prev => [...prev, {
        id: `error-${Date.now()}`,
        type: 'error',
        content: programState.error,
        timestamp: new Date().toLocaleTimeString()
      }])
    }
  }, [programState])

  const handleInputSubmit = (e) => {
    e.preventDefault()
    
    if (!inputValue.trim() || !programState?.waitingForInput) return

    // Add user input to console history
    setConsoleHistory(prev => [...prev, {
      id: `input-${Date.now()}`,
      type: 'input',
      content: inputValue,
      timestamp: new Date().toLocaleTimeString()
    }])

    // Send input to program
    onUserInput(inputValue)
    setInputValue('')
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleInputSubmit(e)
    }
  }

  const clearConsole = () => {
    setConsoleHistory([])
  }

  const renderConsoleEntry = (entry) => {
    switch (entry.type) {
      case 'output':
        return (
          <div key={entry.id} className="console-entry output">
            <span className="console-timestamp">{entry.timestamp}</span>
            <span className="console-content">{entry.content}</span>
          </div>
        )
      
      case 'input':
        return (
          <div key={entry.id} className="console-entry input">
            <span className="console-timestamp">{entry.timestamp}</span>
            <span className="console-prompt">{'>'}</span>
            <span className="console-content">{entry.content}</span>
          </div>
        )
      
      case 'error':
        return (
          <div key={entry.id} className="console-entry error">
            <span className="console-timestamp">{entry.timestamp}</span>
            <span className="console-level">ERROR</span>
            <span className="console-content">{entry.content}</span>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="interactive-console">
      <div className="console-header">
        <div className="console-title">
          <Terminal size={16} />
          <span>Interactive Console</span>
        </div>
        
        <div className="console-controls">
          {programState?.executionTime && (
            <span className="execution-time">
              {programState.executionTime}ms
            </span>
          )}
          
          <button
            className="console-btn clear"
            onClick={clearConsole}
            title="Clear Console"
          >
            Clear
          </button>
          
          {isRunning ? (
            <button
              className="console-btn stop"
              onClick={onStop}
              title="Stop Execution"
            >
              <Square size={14} />
              Stop
            </button>
          ) : (
            <button
              className="console-btn run"
              onClick={onExecute}
              title="Run Program"
            >
              <Play size={14} />
              Run
            </button>
          )}
        </div>
      </div>

      <div className="console-content" ref={consoleRef}>
        {consoleHistory.length === 0 && !isRunning && (
          <div className="console-empty">
            <Terminal size={32} className="empty-icon" />
            <p>Console is ready. Click "Run" to execute your C program.</p>
          </div>
        )}

        {consoleHistory.map(renderConsoleEntry)}

        {isRunning && !programState?.waitingForInput && (
          <div className="console-entry running">
            <span className="console-timestamp">{new Date().toLocaleTimeString()}</span>
            <span className="console-content">
              <span className="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              Program is running...
            </span>
          </div>
        )}

        {programState?.waitingForInput && (
          <div className="console-entry waiting-input">
            <span className="console-timestamp">{new Date().toLocaleTimeString()}</span>
            <span className="console-content">
              <span className="input-prompt">Waiting for input...</span>
            </span>
          </div>
        )}
      </div>

      {programState?.waitingForInput && (
        <div className="console-input-area">
          <form onSubmit={handleInputSubmit} className="input-form">
            <span className="input-prompt">{'>'}</span>
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={programState.inputPrompt || "Enter input and press Enter..."}
              className="console-input"
              disabled={!programState?.waitingForInput}
            />
            <button
              type="submit"
              className="input-submit"
              disabled={!inputValue.trim() || !programState?.waitingForInput}
            >
              Send
            </button>
          </form>
        </div>
      )}
    </div>
  )
}

export default InteractiveConsole
