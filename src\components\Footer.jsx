import { Heart, Code2, Sparkles } from 'lucide-react'
import './Footer.css'

const Footer = () => {
  return (
    <footer className="footer">
      <div className="footer-content">
        <div className="footer-left">
          <div className="credits-container">
            <div className="credits-icon">
              <Code2 size={16} className="code-icon" />
            </div>
            <div className="credits-text">
              <span className="credits-main">
                Bu IDE <span className="developer-name">Tahsin Mert MUTLU</span> tarafından kodlanmıştır.
              </span>
              <div className="credits-subtitle">
                <Sparkles size={12} className="sparkle-icon" />
                <span>Modern web teknolojileri ile geliştirilmiştir</span>
              </div>
            </div>
          </div>
        </div>

        <div className="footer-center">
          <div className="tech-stack">
            <span className="tech-item">React</span>
            <span className="tech-divider">•</span>
            <span className="tech-item">Vite</span>
            <span className="tech-divider">•</span>
            <span className="tech-item">Monaco Editor</span>
            <span className="tech-divider">•</span>
            <span className="tech-item">WebAssembly</span>
          </div>
        </div>

        <div className="footer-right">
          <div className="made-with-love">
            <span>Made with</span>
            <Heart size={14} className="heart-icon" />
            <span>in Turkey</span>
          </div>
          <div className="version-info">
            <span>v1.0.0</span>
          </div>
        </div>
      </div>

      {/* Animated background elements */}
      <div className="footer-bg-elements">
        <div className="bg-element bg-element-1"></div>
        <div className="bg-element bg-element-2"></div>
        <div className="bg-element bg-element-3"></div>
      </div>
    </footer>
  )
}

export default Footer
