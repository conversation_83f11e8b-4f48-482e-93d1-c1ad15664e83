.interactive-console {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.console-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
}

.console-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.console-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.execution-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  background-color: var(--bg-primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

.console-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-primary);
  cursor: pointer;
}

.console-btn.clear {
  background-color: var(--bg-primary);
  color: var(--text-secondary);
}

.console-btn.clear:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.console-btn.run {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.console-btn.run:hover {
  background-color: #059669;
  border-color: #059669;
}

.console-btn.stop {
  background-color: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.console-btn.stop:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

.console-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  line-height: 1.6;
  background-color: var(--bg-secondary);
}

.console-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
  color: var(--text-muted);
  text-align: center;
}

.empty-icon {
  opacity: 0.5;
}

.console-entry {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  word-wrap: break-word;
}

.console-entry.output {
  background-color: rgba(16, 185, 129, 0.05);
  border-left: 3px solid var(--accent-color);
}

.console-entry.input {
  background-color: rgba(37, 99, 235, 0.05);
  border-left: 3px solid var(--primary-color);
}

.console-entry.error {
  background-color: rgba(239, 68, 68, 0.05);
  border-left: 3px solid var(--danger-color);
}

.console-entry.running {
  background-color: rgba(245, 158, 11, 0.05);
  border-left: 3px solid var(--warning-color);
}

.console-entry.waiting-input {
  background-color: rgba(37, 99, 235, 0.1);
  border-left: 3px solid var(--primary-color);
  animation: pulse 2s ease-in-out infinite;
}

.console-timestamp {
  color: var(--text-muted);
  font-size: 0.75rem;
  min-width: 80px;
  flex-shrink: 0;
}

.console-prompt {
  color: var(--primary-color);
  font-weight: 600;
  min-width: 20px;
  flex-shrink: 0;
}

.console-level {
  font-weight: 600;
  min-width: 50px;
  flex-shrink: 0;
  color: var(--danger-color);
}

.console-content-text {
  color: var(--text-primary);
  flex: 1;
  white-space: pre-wrap;
}

.console-entry .console-content {
  color: var(--text-primary);
  flex: 1;
  white-space: pre-wrap;
  padding: 0;
  margin: 0;
  background: none;
  overflow: visible;
}

.input-prompt {
  color: var(--primary-color);
  font-weight: 600;
}

.loading-dots {
  display: inline-flex;
  gap: 2px;
  margin-right: 0.5rem;
}

.loading-dots span {
  width: 4px;
  height: 4px;
  background-color: var(--warning-color);
  border-radius: 50%;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.console-input-area {
  padding: 1rem;
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-primary);
}

.input-form {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.input-form .input-prompt {
  color: var(--primary-color);
  font-weight: 600;
  font-family: var(--font-family-mono);
}

.console-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.console-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.console-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-submit {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.input-submit:hover:not(:disabled) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.input-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animations */
@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .console-header {
    padding: 0.5rem 0.75rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .console-controls {
    gap: 0.25rem;
  }

  .console-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }

  .console-content {
    padding: 0.75rem;
    font-size: 0.75rem;
  }

  .console-timestamp {
    min-width: 60px;
    font-size: 0.6875rem;
  }

  .console-input-area {
    padding: 0.75rem;
  }

  .console-input {
    font-size: 0.75rem;
  }

  .input-submit {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
}
