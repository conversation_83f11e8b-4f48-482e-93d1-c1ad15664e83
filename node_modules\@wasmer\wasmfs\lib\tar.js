"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
// @ts-ignore
const tar_stream_1 = tslib_1.__importDefault(require("tar-stream"));
// @ts-ignore
const pako_inflate_min_js_1 = require("pako/dist/pako_inflate.min.js");
exports.dirname = (path) => {
    return path.replace(/\\/g, "/").replace(/\/[^\/]*$/, "");
};
exports.extractContents = (wasmFs, binary, to) => {
    let volume = wasmFs.volume;
    return new Promise((resolve, reject) => {
        // We create the "to" directory, in case it doesn't exist
        volume.mkdirpBase(to, 0o777);
        // We receive a tar.gz, we first need to uncompress it.
        let inflatedBinary = pako_inflate_min_js_1.inflate(binary);
        // Now, we get the tar contents
        let extract = tar_stream_1.default.extract();
        extract.on("entry", function (header, stream, next) {
            let fullname = `${to}/${header.name}`;
            const chunks = [];
            stream.on("data", (chunk) => chunks.push(chunk));
            // header is the tar header
            // stream is the content body (might be an empty stream)
            // call next when you are done with this entry
            stream.on("end", function () {
                if (header.type === "file") {
                    let contents = Buffer.concat(chunks);
                    // console.log(fullname, contents);
                    try {
                        volume.writeFileSync(fullname, contents);
                    }
                    catch (e) {
                        // The directory is not created yet
                        let dir = exports.dirname(fullname);
                        volume.mkdirpBase(dir, 0o777);
                        volume.writeFileSync(fullname, contents);
                    }
                }
                else if (header.type === "directory") {
                    volume.mkdirpBase(fullname, 0o777);
                }
                next(); // ready for next entry
            });
            stream.resume(); // just auto drain the stream
        });
        extract.on("finish", () => {
            resolve(extract);
        });
        extract.on("error", (err) => {
            reject(err);
        });
        extract.end(Buffer.from(inflatedBinary));
    });
};
