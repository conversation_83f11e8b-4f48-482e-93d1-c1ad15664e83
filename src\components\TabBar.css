.tab-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0;
  min-height: 40px;
}

.tabs {
  display: flex;
  align-items: center;
}

.tab {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: none;
  border-right: 1px solid var(--border-primary);
  cursor: pointer;
  min-width: 120px;
  justify-content: flex-start;
}

.tab:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.tab.active {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-bottom: 2px solid var(--tab-color, var(--primary-color));
}

.tab-icon {
  color: var(--tab-color, var(--text-secondary));
  transition: color var(--transition-fast);
}

.tab.active .tab-icon {
  color: var(--tab-color, var(--primary-color));
}

.tab-name {
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--tab-color, var(--primary-color));
  border-radius: 1px 1px 0 0;
  animation: slideIn 0.2s ease-out;
}

.tab-actions {
  display: flex;
  align-items: center;
  padding: 0 1rem;
  gap: 1rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-family: var(--font-family-mono);
}

.file-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  font-size: 0;
}

.file-status.saved {
  background-color: var(--accent-color);
}

.file-status.modified {
  background-color: var(--warning-color);
}

.file-status.error {
  background-color: var(--danger-color);
}

/* Tab Animations */
@keyframes slideIn {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

/* Tab Hover Effects */
.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--tab-color, var(--primary-color));
  transform: scaleX(0);
  transition: transform var(--transition-fast);
  transform-origin: center;
}

.tab:hover::before {
  transform: scaleX(1);
}

.tab.active::before {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tab {
    min-width: 70px;
    padding: 0.5rem 0.5rem;
    font-size: 0.75rem;
  }

  .tab-name {
    display: none;
  }

  .file-info {
    display: none;
  }
}

@media (max-width: 480px) {
  .tab {
    min-width: 50px;
    padding: 0.375rem 0.25rem;
  }

  .tab-icon {
    width: 14px;
    height: 14px;
  }
}
