.tab-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0;
  min-height: 40px;
}

.tabs {
  display: flex;
  align-items: center;
}

.tab {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  border: none;
  border-right: 1px solid var(--border-primary);
  cursor: pointer;
  min-width: 120px;
  justify-content: flex-start;
}

.tab:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.tab.active {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-bottom: 2px solid var(--tab-color, var(--primary-color));
}

.tab-icon {
  color: var(--tab-color, var(--text-secondary));
  transition: color var(--transition-fast);
}

.tab.active .tab-icon {
  color: var(--tab-color, var(--primary-color));
}

.tab-name {
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--tab-color, var(--primary-color));
  border-radius: 1px 1px 0 0;
  animation: slideIn 0.2s ease-out;
}

.tab-actions {
  display: flex;
  align-items: center;
  padding: 0 1rem;
  gap: 1rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-family: var(--font-family-mono);
}

.file-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  font-size: 0;
}

.file-status.saved {
  background-color: var(--accent-color);
}

.file-status.modified {
  background-color: var(--warning-color);
}

.file-status.error {
  background-color: var(--danger-color);
}

/* Tab Animations */
@keyframes slideIn {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

/* Tab Hover Effects */
.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--tab-color, var(--primary-color));
  transform: scaleX(0);
  transition: transform var(--transition-fast);
  transform-origin: center;
}

.tab:hover::before {
  transform: scaleX(1);
}

.tab.active::before {
  display: none;
}

/* Mobile Layout Styles */
.tab-bar.mobile-tabs {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border-bottom: 2px solid var(--border-secondary);
}

.tab-bar.mobile-tabs .tab {
  min-width: 80px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin: 0 2px;
  transition: all var(--transition-normal);
}

.tab-bar.mobile-tabs .tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tab-bar.mobile-tabs .tab.active {
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  border-bottom: 3px solid var(--tab-color, var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.tab-bar.mobile-tabs .tab-icon {
  width: 18px;
  height: 18px;
}

.tab-bar.mobile-tabs .tab-name {
  display: inline;
  font-weight: 600;
}

.tab-bar.mobile-tabs .file-info {
  background: rgba(37, 99, 235, 0.1);
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

.tab-bar.mobile-tabs .file-name {
  font-size: 0.8125rem;
  font-weight: 500;
}

.tab-bar.mobile-tabs .file-status {
  width: 10px;
  height: 10px;
}

/* Desktop Layout Styles */
.tab-bar.desktop-tabs .tab {
  min-width: 120px;
}

/* Touch-Friendly Enhancements */
.mobile-tabs .tab {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  user-select: none;
}

.mobile-tabs .tab:active {
  transform: scale(0.98);
}

/* Responsive Design Fallback */
@media (max-width: 768px) {
  .tab-bar:not(.mobile-tabs) .tab {
    min-width: 70px;
    padding: 0.5rem 0.5rem;
    font-size: 0.75rem;
  }

  .tab-bar:not(.mobile-tabs) .tab-name {
    display: none;
  }

  .tab-bar:not(.mobile-tabs) .file-info {
    display: none;
  }
}

@media (max-width: 480px) {
  .tab-bar:not(.mobile-tabs) .tab {
    min-width: 50px;
    padding: 0.375rem 0.25rem;
  }

  .tab-bar:not(.mobile-tabs) .tab-icon {
    width: 14px;
    height: 14px;
  }

  /* Mobile layout specific responsive adjustments */
  .tab-bar.mobile-tabs .tab {
    min-width: 70px;
    padding: 0.625rem 0.75rem;
    font-size: 0.8125rem;
  }

  .tab-bar.mobile-tabs .tab-icon {
    width: 16px;
    height: 16px;
  }
}
