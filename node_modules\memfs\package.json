{"name": "memfs", "version": "3.0.4", "description": "In-memory file-system with Node's fs API.", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"clean": "rimraf lib types", "build": "tsc -p . && cpy src/*.js lib", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "watch": "watch \"npm run build\" ./src", "prettier": "prettier --ignore-path .gitignore --write \"src/**/*.{ts,js}\"", "prettier:diff": "prettier -l \"src/**/*.{ts,js}\"", "tslint": "tslint \"src/**/*.ts\" -t verbose", "precommit": "pretty-quick --staged", "prepush": "yarn prettier:diff && yarn tslint"}, "repository": {"type": "git", "url": "https://github.com/streamich/memfs.git"}, "dependencies": {"fast-extend": "1.0.2", "fs-monkey": "0.3.3"}, "devDependencies": {"@types/jest": "23.3.14", "@types/node": "10.17.13", "cpy-cli": "2.0.0", "husky": "1.3.1", "jest": "24.9.0", "prettier": "1.19.1", "pretty-quick": "1.11.1", "rimraf": "2.7.1", "ts-jest": "24.3.0", "ts-node": "8.6.2", "tslint": "5.20.1", "tslint-config-common": "1.6.0", "typescript": "3.7.4", "semantic-release": "15.14.0", "@semantic-release/changelog": "3.0.6", "@semantic-release/git": "7.0.18", "@semantic-release/npm": "5.3.5"}, "config": {"commitizen": {"path": "git-cz"}}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx"], "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": ".*/__tests__/.*\\.(test|spec)\\.(jsx?|tsx?)$"}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"]}, "keywords": ["fs", "filesystem", "fs.js", "memory-fs", "memfs", "file", "file system", "mount", "memory", "in-memory", "virtual", "test", "testing", "mock"]}